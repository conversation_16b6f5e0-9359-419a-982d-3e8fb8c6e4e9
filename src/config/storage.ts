import { registerAs } from '@nestjs/config';

export default registerAs('storage', () => ({
  drive: process.env.STORAGE_DRIVE || 's3',
  prefix: process.env.STORAGE_DIR_PRFIX || '',
  s3: {
    driver: 's3',
    accessKeyId: process.env.AWS_S3_ACCESS_KEY_ID,
    secretAccessKey: process.env.AWS_S3_ACCESS_KEY_SECRET,
    region: process.env.AWS_S3_REGION,
    signatureVersion: 'v4',
    bucketName: process.env.AWS_S3_BUCKET_NAME,
    bucketCompressName: process.env.AWS_S3_BUCKET_COMPRESS_NAME,
    bucketStaticWebsiteName: process.env.AWS_S3_BUCKET_STATIC_WEBSITE_NAME,
    bucketMascotsName: process.env.AWS_S3_BUCKET_MASCOTS_NAME,
    folder: {
      asset: {
        path: '/asset'
      }
    },
    delayTimeToWaitCompressImage:
      (process.env.DELAY_TIME_TO_WAIT_COMPRESSED_IMAGE && parseInt(process.env.DELAY_TIME_TO_WAIT_COMPRESSED_IMAGE)) ||
      1000 * 60 * 5 // 5 minutes
  }
}));
