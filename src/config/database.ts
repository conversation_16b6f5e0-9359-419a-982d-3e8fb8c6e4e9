import * as path from 'path';
import { registerAs } from '@nestjs/config';
import { TypeOrmModuleOptions } from '@nestjs/typeorm/dist/interfaces/typeorm-options.interface';

export default registerAs(
  'database',
  (): TypeOrmModuleOptions => ({
    type: 'postgres',
    host: process.env.TYPEORM_HOST,
    username: process.env.TYPEORM_USERNAME,
    password: process.env.TYPEORM_PASSWORD,
    database: process.env.TYPEORM_DATABASE,
    // @ts-ignore
    bigNumberStrings: false,
    port: parseInt(process.env.TYPEORM_PORT, 10) || 5432,
    charset: 'UTF8',
    logging: process.env.TYPEORM_LOGGING === 'true',
    entities: process.env.TYPEORM_ENTITIES
      ? process.env.TYPEORM_ENTITIES.split(',').map(entityPath => {
          return path.join(path.dirname(__dirname), entityPath.replace('src', '').replace(/\.ts/g, '.{ts,js}'));
        })
      : [
          path.join(path.dirname(__dirname), 'core', '**', 'entity', '*.entity.{ts,js}'),
          path.join(path.dirname(__dirname), 'module', '**', 'entity', '*.entity.{ts,js}')
        ],
    synchronize: process.env.TYPEORM_SYNCHRONIZE === 'true',
    migrationsTableName: 'migrations',
    migrationsRun: process.env.TYPEORM_MIGRATIONS_RUN === 'true',
    migrations: process.env.TYPEORM_MIGRATIONS
      ? process.env.TYPEORM_MIGRATIONS.split(',').map(migrationPath => {
          return path.join(path.dirname(__dirname), migrationPath.replace('src', '').replace(/\.ts/g, '.{ts,js}'));
        })
      : [
          path.join(path.dirname(__dirname), 'core', '**', 'migration', '!(*.d).{ts,js}'),
          path.join(path.dirname(__dirname), 'module', '**', 'migration', '!(*.d).{ts,js}')
        ],
    cli: {
      migrationsDir: 'src/migration',
      subscribersDir: 'src/subscriber'
    },
    subscribers: process.env.TYPEORM_SUBSCRIBERS
      ? process.env.TYPEORM_SUBSCRIBERS.split(',').map(subscriberPath => {
          return path.join(path.dirname(__dirname), subscriberPath.replace('src', '').replace(/\.ts/g, '.{ts,js}'));
        })
      : [
          path.join(path.dirname(__dirname), 'core', '**', 'subscriber', '*.subscriber.js'),
          path.join(path.dirname(__dirname), 'module', '**', 'subscriber', '*.subscriber.js')
        ],
    extra: { timezone: 'utc' },
    retryAttempts: 3
  })
);
