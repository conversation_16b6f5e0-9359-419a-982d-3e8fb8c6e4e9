import { registerAs } from '@nestjs/config';

export default registerAs('cache', () => ({
  store: process.env.CACHE_STORAGE || 'memory',
  redis: {
    host: process.env.CACHE_STORAGE_REDIS_HOST,
    port: process.env.CACHE_STORAGE_REDIS_PORT,
    auth_pass: process.env.CACHE_STORAGE_REDIS_AUTH_PASS,
    db: 0,
    max: 1000,
    ttl: process.env.CACHE_TTL || 100
  },
  memory: {
    store: 'memory',
    max: 100,
    ttl: process.env.CACHE_TTL || 60
  }
}));
