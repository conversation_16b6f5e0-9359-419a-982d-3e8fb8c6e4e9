import { registerAs } from '@nestjs/config';

export default registerAs('moderation', () => ({
  type: 'service_account',
  project_id: process.env.MODERATION_PROJECT_ID || '',
  private_key_id: process.env.MODERATION_PRIVATE_KEY_ID || '',
  private_key: (process.env.MODERATION_PRIVATE_KEY || '').replace(/\\n/g, '\n'),
  client_email: process.env.MODERATION_CLIENT_EMAIL || '',
  client_id: process.env.MODERATION_CLIENT_ID || '',
  auth_uri: process.env.MODERATION_AUTH_URI || '',
  token_uri: process.env.MODERATION_TOKEN_URI || '',
  auth_provider_x509_cert_url: process.env.MODERATION_AUTH_PROVIDER_X509_CERT_URL || '',
  client_c509_cert_url: process.env.MODERATION_CLIENT_C509_CERT_URL || '',
  universe_domain: process.env.MODERATION_UNIVERSE_DOMAIN || ''
}));
