import { registerAs } from '@nestjs/config';

export default registerAs('mail', () => ({
  from: {
    name: process.env.MAIL_FROM_NAME || 'Commun1ty Dev',
    email: process.env.MAIL_FROM || '<EMAIL>'
  },
  data: {},
  transporter: process.env.MAIL_TRANSPORT || 'sendgridMail',
  smtp: {
    host: process.env.MAIL_HOST,
    port: process.env.MAIL_PORT,
    username: process.env.MAIL_USERNAME,
    password: process.env.MAIL_PASSWORD
  },
  sendgridMail: {
    apiKey: process.env.MAIL_PASSWORD
  }
}));
