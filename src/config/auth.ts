import { registerAs } from '@nestjs/config';

export default registerAs('auth', () => ({
  jwt: {
    secret: process.env.JWT_AUTH_SECRET_KEY || 'This is jwt secret key',
    expiresIn: process.env.JWT_AUTH_EXPIRES_IN || 8 * 60 * 60 + 's'
  },
  refreshTokenExpiresIn: process.env.JWT_AUTH_REFRESH_EXPIRES_IN || 7 * 24 * 60 * 60 + 's',
  resetPasswordTokenExpiresIn: 30 + ' minutes',
  activateBy: ['email', 'phoneNumber']
}));
