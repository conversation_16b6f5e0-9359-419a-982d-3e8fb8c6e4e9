import { registerAs } from '@nestjs/config';

export default registerAs('payment', () => ({
  stripe: {
    secretKey: process.env.PAYMENT_STRIPE_SECRET_KEY,
    publicKey: process.env.PAYMENT_STRIPE_PUBLIC_KEY,
    endPointSecretKey: process.env.PAYMENT_STRIPE_END_POINT_SECRET,
    options: {
      apiVersion: process.env.PAYMENT_STRIPE_API_VERSION,
      appInfo: {
        name: process.env.PAYMENT_STRIPE_APP_NAME
      }
    }
  }
}));
