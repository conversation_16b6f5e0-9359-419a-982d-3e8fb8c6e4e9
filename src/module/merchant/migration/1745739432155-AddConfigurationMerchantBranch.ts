import Column from '@core/database/schema/column';
import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddConfigurationMerchantBranch1745739432155 implements MigrationInterface {
  tableName = 'merchants_branches';

  public async up(queryRunner: QueryRunner): Promise<any> {
    if (await queryRunner.hasTable(this.tableName)) {
      if (!(await queryRunner.hasColumn(this.tableName, 'configuration'))) {
        await queryRunner.addColumns(this.tableName, [Column.jsonb('configuration').nullable()]);
      }
    }
  }

  public async down(queryRunner: QueryRunner): Promise<any> {
    if (await queryRunner.hasColumn(this.tableName, 'configuration')) {
      await queryRunner.dropColumn(this.tableName, 'configuration');
    }
  }
}
