import BaseEntity from '@core/database/entity/base-entity';
import { En<PERSON><PERSON>, Column, PrimaryColumn, UpdateDateColumn, CreateDateColumn, ManyToOne, JoinColumn } from 'typeorm';
import { UserEntity } from '@core/user/entity/user.entity';

@Entity('bot_configs')
export class BotConfigEntity extends BaseEntity {
  @PrimaryColumn('uuid')
  id: string;

  @Column({ default: true })
  enabled: boolean;

  // Default cron schedule: run at a random minute between 8 AM and 11 PM daily
  // The EventScannerJobService will further randomize this within the range.
  @Column({ default: '0 0 8-23 * * *' }) // This is a placeholder. The job service will refine this.
  cronSchedule: string;

  @Column({
    type: 'jsonb',
    default: {
      url: 'https://www.eventbrite.com/d/singapore/all-events/',
      maxPages: 10,
      waitTimeMs: 20000,
      enabled: true
    }
  })
  eventbrite: {
    url: string;
    maxPages: number;
    waitTimeMs: number;
    enabled: boolean;
    // Add more fields here if needed later
  };

  @Column({
    type: 'jsonb',
    default: {
      enabled: true
    },
    name: 'gemini_ai_generation'
  })
  geminiAiGeneration: {
    enabled: boolean;
    // Add more AI generation configs here if needed later
  };

  @Column({ name: 'default_event_owner_id', type: 'uuid' })
  defaultEventOwnerId: string;

  @Column({ name: 'created_by', type: 'uuid' })
  createdBy: string;

  @Column({ name: 'updated_by', type: 'uuid' })
  updatedBy: string;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  @ManyToOne(() => UserEntity, { nullable: true })
  @JoinColumn({ name: 'default_event_owner_id' })
  defaultEventOwner?: UserEntity;
}
