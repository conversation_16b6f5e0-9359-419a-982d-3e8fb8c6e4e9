import Column from '@core/database/schema/column';
import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddEventCreator1749914150279 implements MigrationInterface {
  tableName = 'bot_configs';

  public async up(queryRunner: QueryRunner): Promise<any> {
    if (await queryRunner.hasTable(this.tableName)) {
      if (!(await queryRunner.hasColumn(this.tableName, 'default_event_owner_id'))) {
        await queryRunner.addColumns(this.tableName, [Column.uuid('default_event_owner_id').nullable()]);
      }
    }
  }

  public async down(queryRunner: QueryRunner): Promise<any> {
    if (await queryRunner.hasColumn(this.tableName, 'default_event_owner_id')) {
      await queryRunner.dropColumn(this.tableName, 'default_event_owner_id');
    }
  }
}
