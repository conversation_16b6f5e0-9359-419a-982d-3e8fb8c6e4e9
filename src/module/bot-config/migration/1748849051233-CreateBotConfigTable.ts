import { Schema } from '@core/database/schema/schema';
import { MigrationInterface, QueryRunner } from 'typeorm';
import Column, { STRING_SHORT_LENGTH } from '@core/database/schema/column';

export class CreateBotConfigTable1748849051233 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<any> {
    await queryRunner.createTable(
      Schema.define('bot_configs', [
        Column.uuid('id').setPrimary(),
        Column.boolean('enabled').nullable().setDefault(false),
        Column.string('cronSchedule', STRING_SHORT_LENGTH).nullable(),
        Column.jsonb('eventbrite').nullable(),
        Column.jsonb('gemini_ai_generation').nullable(),
        Column.timestamp('created_at').nullable(),
        Column.timestamp('updated_at').nullable(),
        Column.uuid('created_by').nullable(),
        Column.uuid('updated_by').nullable()
      ]),
      true
    );
  }

  public async down(queryRunner: QueryRunner): Promise<any> {
    await queryRunner.dropTable('bot_configs', true);
  }
}
