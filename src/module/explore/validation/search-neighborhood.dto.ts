import { IsIn, IsOptional, IsString, IsUUID } from 'class-validator';

import { SearchNeighborhoodDto } from '@module/neighborhood/validation/search-neighborhood.dto';

export class SearchNeighborhoodCouponDto extends SearchNeighborhoodDto {
  @IsOptional()
  @IsString()
  @IsIn(['endTime', 'startTime', 'title', 'updatedAt', 'createdAt', undefined])
  orderBy: any = undefined;

  @IsUUID()
  @IsOptional()
  lastExpiredCouponId: string;
}
