/* eslint-disable max-len */
import { Injectable, Logger } from '@nestjs/common';
import { GeminiService } from '@module/ai/service/gemini.service';
import * as dayjs from 'dayjs';
import { PostEntity } from '@module/post/entity/post.entity';

@Injectable()
export class GeminiEventService {
  private readonly logger = new Logger(GeminiEventService.name);

  constructor(private readonly geminiService: GeminiService) {}

  /**
   * Get events from Gemini AI
   * @param eventsToAvoid List of event titles to avoid (duplicates)
   * @returns List of events
   */
  async getEvents(eventsToAvoid: string[] = []): Promise<PostEntity[]> {
    this.logger.log('Using AI to find events');

    return this.generateEventsByAI(eventsToAvoid);
  }

  private parseCustomJSONString(customString: string) {
    try {
      const cleanedString = customString.replace(/`([^`]+)`/g, (_, content) => {
        const escapedContent = content.replace(/\n/g, '\\n').replace(/"/g, '\\"');

        return `"${escapedContent}"`;
      });

      const jsonObject = JSON.parse(cleanedString);

      return jsonObject;
    } catch (error) {
      this.logger.error('Failed to parse JSON:', error.message, error.stack);

      return null;
    }
  }

  /**
   * Generate sample events using AI when API key is not available
   * @param eventsToAvoid List of event titles to avoid (duplicates)
   * @returns List of sample events
   */
  private async generateEventsByAI(eventsToAvoid: string[] = []): Promise<PostEntity[]> {
    try {
      this.logger.log('Finding events using AI...');

      // Determine if we're in the last 3 months of the year
      const today = new Date();

      const currentDate = dayjs(today).format('MMMM D, YYYY');
      const next1Month = dayjs(today).add(1, 'month').endOf('month').format('MMMM D, YYYY');

      // Add events to avoid in the prompt if there are any
      const avoidEventsText =
        eventsToAvoid.length > 0
          ? `\nIMPORTANT: Do NOT include these events as they are duplicates: ${eventsToAvoid.join(', ')}.`
          : '';

      const prompt = `
        Find a JSON array containing exactly 10 real upcoming events in Singapore, scheduled between ${currentDate} and ${next1Month}, strictly in Singapore Time (SGT).

        Requirements:
        + Only include real events — no fabricated or fictional entries.
        + Event dates must be exact and fall within the specified range.
        + VenueName and VenueAddress must be valid and match the actual location of the event.
        + Include google map link in MoreInformation object.${avoidEventsText}

        Each event should include the following fields:

        - title
        - startDate
        - endDate
        - eventTime
        - venue
        - address
        - eventType
        - organizer
        - ticketInfo
        - description
        - eventLink (official event link)
        - googleMapLink (link to google map)
        - more (Object of more information)

        Output format: JSON array of exactly 10 event objects.
      `;

      let aiResponse = await this.geminiService.generateContent(prompt);
      aiResponse = aiResponse.replace('```html', '').replace('```json', '').replace(/```/g, '').replace(/\n/g, '');

      const eventsData = this.parseCustomJSONString(aiResponse);

      if (!eventsData) {
        throw new Error('Failed to parse AI-generated events');
      }

      // Map the AI-generated events to our Event model
      return eventsData;
    } catch (error) {
      this.logger.error(`Error generating sample events: ${error.message}`, error.stack);

      return [];
    }
  }
}
