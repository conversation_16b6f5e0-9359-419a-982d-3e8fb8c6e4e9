import puppeteer, { <PERSON><PERSON><PERSON>, <PERSON> } from 'puppeteer';
import { PostEntity } from '../entity/post.entity';
import { POST_STATUS } from '../const/post-status';
import { POST_SOURCE } from '../const/post-source';
import { POST_TYPE } from '../const/post-type';
import { v4 as uuid } from 'uuid';
import { UserEntity } from '@core/user/entity/user.entity';
import { USER_STATUS } from '@core/user/const/user-status';
import * as dayjs from 'dayjs';
import { Injectable } from '@nestjs/common';
import { BotConfigService } from '@module/bot-config/service/bot-config.service';
import { StorageService } from '@core/storage/storage.service';
import axios from 'axios';
import { htmlToPlainText } from '@core/util/htmlToPlainText';
import { sleep } from '@core/util/sleep';
import { ConfigService } from '@nestjs/config';

// using puppeteer to scan the events in singapore
@Injectable()
export class EventScrapingScannerService {
  constructor(
    private readonly botConfigService: BotConfigService,
    private readonly storageService: StorageService,
    private readonly configService: ConfigService
  ) {}

  async scanEventsByEventBrite(): Promise<any[]> {
    let browser: Browser | undefined;
    try {
      const config = await this.botConfigService.getConfig();
      const eventbriteConfig = config?.eventbrite;

      if (!eventbriteConfig?.url) {
        return [];
      }
      const isProduction = this.configService.get<string>('app.env') === 'production';

      browser = await puppeteer.launch({ headless: 'new' });
      const page: Page = await browser.newPage();
      await page.goto(eventbriteConfig.url, { waitUntil: 'domcontentloaded' });

      const outputEventPosts: PostEntity[] = [];

      const randomUsers = await UserEntity.createQueryBuilder('usr')
        .select(['usr.id'])
        .where({ status: USER_STATUS.activated, isDummy: true })
        .orderBy('RANDOM()')
        .limit(20)
        .getMany();

      const processedEventIds = new Set<string>();

      let currentPage = 1;

      const searchData = await page.evaluate(() => {
        return (window as any)?.__SERVER_DATA__?.search_data || {};
      });

      // Use configured max pages
      const maxPages = searchData.events?.pagination?.page_count || eventbriteConfig.maxPages || 10;
      console.log('---> scanEventsByEventBrite maxPages', maxPages);

      let stopScraping = false;
      while (!stopScraping) {
        console.log(`⏳ Scraping page ${currentPage}`);

        const searchData = await page.evaluate(() => {
          return (window as any)?.__SERVER_DATA__?.search_data || {};
        });

        if (!searchData?.events?.results?.length) break;

        const eventIds = searchData.events.results.map(event => event.id);

        const existEventPosts = await PostEntity.createQueryBuilder('pst')
          .where("pst.more_information->>'id' IN (:...eventIds)", { eventIds })
          .select(['pst.moreInformation'])
          .getMany();

        const existingIds = new Set(existEventPosts.map(p => p.moreInformation?.id));

        for (const event of searchData.events.results) {
          const eventId = event.id;

          if (
            processedEventIds.has(eventId) ||
            existingIds.has(eventId) ||
            (event.end_date && dayjs(event.end_date).isBefore(dayjs()))
          ) {
            continue;
          }

          const newPostEntity = new PostEntity();
          newPostEntity.id = uuid();
          newPostEntity.title = event?.name || '';
          newPostEntity.status = POST_STATUS.activate;
          newPostEntity.source = POST_SOURCE.crawler;
          newPostEntity.type = POST_TYPE.invite;
          newPostEntity.message = event.summary || '';
          newPostEntity.address = event.primary_venue?.name || '';
          newPostEntity.startDate = event.start_date;
          newPostEntity.endDate = event.end_date;

          const singaporeTime = '+08:00';
          if (newPostEntity.startDate && event.start_time) {
            const dateString = `${newPostEntity.startDate}T${event.start_time}${singaporeTime}`;
            newPostEntity.startDate = new Date(dateString);
          }
          if (newPostEntity.endDate && event.end_time) {
            const dateString = `${newPostEntity.endDate}T${event.end_time}${singaporeTime}`;
            newPostEntity.endDate = new Date(dateString);
          }
          newPostEntity.moreInformation = {
            source: 'EventBrite',
            ...event
          };

          if (event.primary_venue?.address?.latitude && event.primary_venue?.address?.longitude) {
            newPostEntity.location = {
              lat: event.primary_venue.address.latitude,
              long: event.primary_venue.address.longitude
            };
          }

          // Set createdBy: use defaultEventOwnerId from config if present, else random dummy user
          const defaultEventOwnerId = config?.defaultEventOwnerId;
          if (defaultEventOwnerId) {
            newPostEntity.createdBy = defaultEventOwnerId;
          } else if (randomUsers.length) {
            const createdBy = randomUsers[Math.floor(Math.random() * randomUsers.length)]?.id || randomUsers[0].id;
            newPostEntity.createdBy = createdBy;
          }

          outputEventPosts.push(newPostEntity);
          processedEventIds.add(eventId);

          //   for now daily post will generate at limit 50 posts/day
          const maxPostPerDay = isProduction ? 50 : 20;
          if (outputEventPosts.length >= maxPostPerDay) {
            stopScraping = true;
            break;
          }
        }

        console.log('outputEventPosts', outputEventPosts.length);

        if (stopScraping) {
          break;
        }

        // Stop if last page or maxPages reached
        if (
          !searchData.events?.pagination ||
          searchData.events.pagination.page_number >= searchData.events.pagination.page_count ||
          currentPage >= maxPages
        ) {
          break;
        }

        const waitTimeMs = eventbriteConfig.waitTimeMs || 20000;
        console.log('---> waiting for %dms before clicking on next page button', waitTimeMs);
        await page.waitForTimeout(waitTimeMs);
        try {
          const nextButton = await page.$('.search-results-panel-content footer button[aria-label="Next Page"]');
          if (nextButton) {
            await nextButton.click();
            console.log('---> waiting for %dms after clicking on next page button', waitTimeMs);
            await page.waitForTimeout(waitTimeMs);

            console.log('---> reload page after clicking next button');
            await page.reload({ waitUntil: 'domcontentloaded' });
          } else {
            break;
          }
        } catch (err) {
          console.error(`⚠️ Failed to go to next page on page ${currentPage}:`, err);
          break;
        }

        currentPage++;
      }

      // Optional: Save to DB here
      if (outputEventPosts.length > 0) {
        const _saveOutput = await Promise.all(
          outputEventPosts.map(async p => {
            try {
              if (p.moreInformation?.image?.url) {
                // using aws to save the http image and the save into post entity
                const fileName = `${p.moreInformation?.id || uuid()}_image`;

                const imageBuffer = await axios
                  .get(p.moreInformation?.image?.url, { responseType: 'arraybuffer' })
                  .then(res => res.data);

                const { keyName } = await this.storageService.putObject(fileName, { Body: imageBuffer });
                p.photos = [keyName];

                this.storageService.compressImage(keyName);
              }
            } catch (_err) {
              // ignore error
            }

            return p;
          })
        );

        // go to detail to fetch event detail data
        for (const p of _saveOutput) {
          if (p.moreInformation?.url) {
            try {
              await page.goto(p.moreInformation?.url, { waitUntil: 'domcontentloaded' });
              const eventDetailResponse = await page.evaluate(() => {
                return (window as any)?.__SERVER_DATA__ || {};
              });
              const eventDetails = {
                event: eventDetailResponse?.event,
                event_listing_response: eventDetailResponse?.event_listing_response,
                user: eventDetailResponse?.user,
                organizer: eventDetailResponse?.organizer
              };
              p.moreInformation.eventDetails = eventDetails;
              if (eventDetails?.event_listing_response?.structuredContent?.modules?.length) {
                const modules = eventDetails?.event_listing_response?.structuredContent?.modules;
                const textDescription = modules.find(m => m.type === 'text')?.text;
                if (textDescription) {
                  const updatedMsg = htmlToPlainText(textDescription);
                  if (updatedMsg) {
                    p.message = updatedMsg;
                  }
                }
              }
              await sleep(10000);
            } catch (_err) {}
          }
        }

        await PostEntity.save(_saveOutput);
      }

      console.log(`✅ Finished scraping ${outputEventPosts.length} new posts.`);

      return outputEventPosts;
    } catch (error) {
      console.error('❌ Error scanning events:', error);

      return [];
    } finally {
      if (browser) await browser.close();
    }
  }
}
