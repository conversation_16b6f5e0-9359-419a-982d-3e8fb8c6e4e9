import { Injectable, Logger } from '@nestjs/common';
import { GeminiEventService } from '@module/post/service/sources/gemini-event.service';
import { PostEntity } from '@module/post/entity/post.entity';
import { POST_SOURCE } from '@module/post/const/post-source';
import { POST_TYPE } from '@module/post/const/post-type';
import { UserEntity } from '@core/user/entity/user.entity';
import { USER_STATUS } from '@core/user/const/user-status';
import { POST_STATUS } from '@module/post/const/post-status';
import { GooglePlaceService } from '@module/place/service/google-place.service';
import { v4 as uuid } from 'uuid';
import { PlaceService } from '@module/place/service/place.service';
import { PlaceEntity } from '@module/place/entity/place.entity';
import { PostPlaceEntity } from '@module/post/entity/post-place.entity';
import { DeepPartial } from 'typeorm';

@Injectable()
export class EventAiScannerService {
  private readonly logger = new Logger(EventAiScannerService.name);

  constructor(
    private readonly geminiEventService: GeminiEventService,
    private readonly googlePlaceService: GooglePlaceService
  ) {}

  /**
   * Scan for events from all sources
   * @returns A list of events
   * @param retryAttempt Whether this is a retry attempt (to avoid infinite loops)
   * @param eventsToAvoid List of event titles to avoid (duplicates)
   */
  async scanEvents(retryAttempt = false, eventsToAvoid: string[] = []): Promise<PostEntity[]> {
    this.logger.log(`${retryAttempt ? 'Retrying event scan' : 'Starting event scan'}...`);

    try {
      // Get events from Gemini AI, passing events to avoid if any
      const events = await this.geminiEventService.getEvents(eventsToAvoid);

      // Sort events by start date
      const sortedEvents = this.sortEventsByDate(events);

      this.logger.log(`Found ${sortedEvents.length} unique events`);

      // Save events to JSON file and get info about duplicates
      const { savedCount, duplicateCount, duplicateEventTitles } = await this.saveEventPosts(sortedEvents);

      // If all events were duplicates and this is not already a retry attempt, try again
      if (savedCount === 0 && duplicateCount > 0 && !retryAttempt) {
        this.logger.log('All events were duplicates. Retrying with new events...');

        // Pass the duplicate event titles to avoid generating them again
        return this.scanEvents(true, duplicateEventTitles); // Retry once with retryAttempt=true to avoid infinite loops
      }

      return sortedEvents;
    } catch (error) {
      this.logger.error(`Error scanning events: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Sort events by start date
   * @param events List of events
   * @returns Sorted list of events
   */
  private sortEventsByDate(events: PostEntity[]): PostEntity[] {
    return events.sort((a, b) => {
      return new Date(a.startDate).getTime() - new Date(b.startDate).getTime();
    });
  }

  /**
   * Save events to a JSON file
   * @param events List of events
   * @returns Object containing saved count, duplicate count, duplicate event titles, and file path
   */
  async saveEventPosts(
    events: PostEntity[]
  ): Promise<{ savedCount: number; duplicateCount: number; duplicateEventTitles: string[] }> {
    // Get random users to assign as creators
    const randomUser = await UserEntity.createQueryBuilder('usr')
      .select(['usr.id'])
      .where({ status: USER_STATUS.activated })
      .andWhere({ isDummy: true })
      .orderBy('RANDOM()')
      .limit(events.length)
      .getOne();

    // Check for duplicates by title and start date
    const newPostsToSave: PostEntity[] = [];
    const duplicateEvents = [];

    for (const evt of events) {
      // Check if event with same title and start date already exists
      const existingEvent = await PostEntity.createQueryBuilder('pst')
        .where('pst.title = :title', { title: evt.title })
        .andWhere('pst.startDate = :startDate', { startDate: evt.startDate })
        .andWhere('pst.source = :source', { source: POST_SOURCE.aiGeneration })
        .getOne();

      if (existingEvent) {
        duplicateEvents.push(evt.title);
        continue;
      }

      const newPostEntity = new PostEntity();
      newPostEntity.id = uuid();
      newPostEntity.status = POST_STATUS.draft;
      newPostEntity.source = POST_SOURCE.aiGeneration;
      newPostEntity.type = POST_TYPE.invite;
      newPostEntity.message = (evt as any).description || newPostEntity.message;
      newPostEntity.moreInformation = evt;
      Object.assign(newPostEntity, evt);

      // Assign a random user as the creator if available
      if (randomUser) {
        newPostEntity.createdBy = randomUser.id;
      }

      newPostsToSave.push(newPostEntity);
    }

    if (duplicateEvents.length > 0) {
      const duplicateList = duplicateEvents.slice(0, 3).join(', ');
      const ellipsis = duplicateEvents.length > 3 ? '...' : '';
      this.logger.log(`Skipped ${duplicateEvents.length} duplicate events: ${duplicateList}${ellipsis}`);
    }

    let savedCount = 0;

    if (newPostsToSave.length > 0) {
      // todo find place

      const newPostPlacesToSave: DeepPartial<PostPlaceEntity>[] = [];
      const _newPostsToSave = await Promise.all(
        newPostsToSave.map(async pst => {
          try {
            if (pst.moreInformation?.venue && !String(pst.moreInformation.venue).toLowerCase().includes('various')) {
              let inputTextSearch = pst.moreInformation.address || pst.moreInformation?.venue;

              if (!inputTextSearch.toLowerCase().includes('singapore')) {
                inputTextSearch += ', singapore';
              }

              const place = await this.googlePlaceService
                .searchAutocomplete({
                  input: inputTextSearch,
                  sessiontoken: uuid()
                })
                .then(res => res.predictions?.[0]);

              if (place) {
                const placeEntity = (await PlaceService.savePlaceByGooglePlaceId(place.place_id, {
                  returnValue: 'placeObject'
                })) as PlaceEntity;

                newPostPlacesToSave.push({
                  postId: pst.id,
                  placeId: placeEntity.id,
                  createdBy: pst.createdBy
                });
                pst.address = placeEntity.address;
                pst.location = placeEntity.location;
              }
            }
          } catch (error) {
            this.logger.error(`Save event post - Error finding place: ${pst.title}`, error);
          }

          return pst;
        })
      );
      const savedEventPosts = await PostEntity.save(_newPostsToSave);
      savedCount = savedEventPosts.length;
      this.logger.log(`Saved ${savedCount} new posts to database`);

      if (newPostPlacesToSave.length) {
        PostPlaceEntity.save(newPostPlacesToSave).then(results => {
          this.logger.log(`Saved ${results.length} new post places to database`);
        });
      }
    } else {
      this.logger.log('No new events to save to database');
    }

    return {
      savedCount,
      duplicateCount: duplicateEvents.length,
      duplicateEventTitles: duplicateEvents
    };
  }
}
