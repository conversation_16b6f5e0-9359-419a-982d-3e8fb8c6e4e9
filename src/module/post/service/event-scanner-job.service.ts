/* eslint-disable max-len */

import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { SchedulerRegistry } from '@nestjs/schedule';
import { CronJob } from 'cron';
import { EventAiScannerService } from '@module/post/service/event-ai-scanner.service';
import { EventScrapingScannerService } from './event-scraping-scanner.service';
import { BotConfigService } from '@module/bot-config/service/bot-config.service';
import * as cronParser from 'cron-parser';
import { DatabaseTransactionService } from '@core/database/support/transaction';
import { UserEntity } from '@core/user/entity/user.entity';
import { UserProfileEntity } from '@core/user/entity/user-profile.entity';
import { USER_STATUS } from '@core/user/const/user-status';
import { UserMascotService } from '@module/mascot/service/user-mascot.service';
import { MascotService } from '@module/mascot/service/mascot.service';
import { genSaltSync, hashSync } from 'bcryptjs';

@Injectable()
export class EventScannerJobService implements OnModuleInit {
  private readonly logger = new Logger(EventScannerJobService.name);

  constructor(
    private readonly schedulerRegistry: SchedulerRegistry,
    private readonly eventAiScannerService: EventAiScannerService,
    private readonly eventScrapingScannerService: EventScrapingScannerService,
    private readonly botConfigService: BotConfigService,
    private readonly databaseTransactionService: DatabaseTransactionService,
    private readonly userMascotService: UserMascotService,
    private readonly mascotService: MascotService
  ) {}

  async onModuleInit() {
    await this.ensureComun1tyEventUser();
    this.setupCronJob();
  }

  /**
   * Ensure comun1ty_event user exists, create if not
   */
  private async ensureComun1tyEventUser() {
    const savedUser = await this.databaseTransactionService.run(async entityManager => {
      const existing = await entityManager.findOne(UserEntity, {
        where: { username: 'commun1ty_event' },
        relations: ['profile']
      });

      if (existing) return existing;
      const user = new UserEntity();
      user.username = 'commun1ty_event';
      user.email = '<EMAIL>';
      const salt = genSaltSync();
      user.salt = salt;
      user.password = hashSync(Math.random().toString(36).substring(2, 15), salt);
      user.activated = new Date();
      user.isVerifiedEmail = new Date();
      user.isVerifiedPhoneNumber = new Date();
      user.isKYC = new Date();
      user.status = USER_STATUS.activated;
      user.isDummy = false;
      const savedUser = await entityManager.save(UserEntity, user);
      const profile = new UserProfileEntity();
      profile.userId = savedUser.id;
      profile.firstName = 'Commun1ty';
      profile.lastName = 'Event';

      await entityManager.save(UserProfileEntity, profile);
      this.logger.log('Created comun1ty_event user and profile.');

      return savedUser;
    });

    if (savedUser && !savedUser.profile?.mascotAvatar) {
      const randomMascot = await this.mascotService.getRandomMascot({});

      await this.userMascotService.userSaveMascotAvatar(
        {
          color: randomMascot.color.hex,
          faceType: randomMascot.face?.sectionType,
          accessory: randomMascot.accessory?.key,
          outfit: randomMascot.outfit?.key
        },
        savedUser.id
      );
    }
  }

  /**
   * Set up the cron job to run the event scanner
   * This will run based on the configured schedule
   */
  private async setupCronJob() {
    // Remove any existing scheduler job first
    try {
      this.schedulerRegistry.deleteCronJob('event-scanner-scheduler');
    } catch (e) {
      // Job doesn't exist yet, which is fine
    }

    // Schedule the job to run daily at 23:59:59
    const fixedSchedule = '59 59 23 * * *';
    const schedulerJob = new CronJob(fixedSchedule, () => {
      this.scheduleScansBasedOnConfig();
    });

    this.schedulerRegistry.addCronJob('event-scanner-scheduler', schedulerJob);
    schedulerJob.start();

    this.logger.log(`Event scanner main scheduler set up with schedule: ${fixedSchedule}`);

    // Schedule the first scan immediately based on current config
    this.scheduleScansBasedOnConfig();
  }

  /**
   * Schedule one-time scan jobs for today based on the latest config.
   * This method is called by the main cron job and on module init.
   */
  private async scheduleScansBasedOnConfig() {
    const config = await this.botConfigService.getConfig();

    console.log('config', config);

    // Clear any previously scheduled one-time scan jobs for today
    const jobs = this.schedulerRegistry.getCronJobs();
    jobs.forEach((job, name) => {
      if (name.startsWith('event-scan-today-')) {
        this.schedulerRegistry.deleteCronJob(name);
        this.logger.log(`Removed previous one-time job: ${name}`);
      }
    });

    const now = new Date();
    const todayString = now.toISOString().split('T')[0];

    const cronSchedule = config?.cronSchedule || '';

    if (!cronSchedule) {
      this.logger.log('No cron schedule found. Will not schedule any scans.');

      return;
    }

    let usingCronSchedule = false;
    // validate cron schedule
    try {
      cronParser.parseExpression(cronSchedule);
      usingCronSchedule = true;
    } catch (e) {
      this.logger.error(e);
      this.logger.warn('Invalid cron schedule. Will use a random time to schedule the job.');
    }

    // Schedule Eventbrite scan if enabled
    if (config?.eventbrite?.enabled) {
      // using cronSchedule to set con job
      if (usingCronSchedule) {
        const job = new CronJob(cronSchedule, () => {
          this.logger.log(`Running scheduled Eventbrite scan at ${new Date().toLocaleString()}...`);
          this.eventScrapingScannerService.scanEventsByEventBrite();
        });
        this.schedulerRegistry.addCronJob('event-scan-today-eventbrite-cron', job);
        job.start();
        this.logger.log(`Eventbrite scan job scheduled with cron schedule: ${cronSchedule}`);
      } else {
        // using random time to set one-time job
        const randomHour = Math.floor(Math.random() * 16) + 8; // 8 to 23
        const randomMinute = Math.floor(Math.random() * 60); // 0 to 59
        const scheduledTime = new Date(now.getFullYear(), now.getMonth(), now.getDate(), randomHour, randomMinute);

        // Only schedule if the random time hasn't passed today
        if (scheduledTime > now) {
          const jobName = `event-scan-today-eventbrite-${todayString}`;
          const job = new CronJob(
            scheduledTime,
            () => {
              this.logger.log('Running scheduled Eventbrite scan...');
              this.eventScrapingScannerService.scanEventsByEventBrite();
            },
            null,
            true
          );

          this.schedulerRegistry.addCronJob(jobName, job);
          const formattedMinute = randomMinute < 10 ? '0' + randomMinute : randomMinute;
          this.logger.log(`Eventbrite scan job scheduled for today at ${randomHour}:${formattedMinute}`);
        } else {
          const formattedMinute = randomMinute < 10 ? '0' + randomMinute : randomMinute;
          this.logger.log(
            `Random time ${randomHour}:${formattedMinute} for Eventbrite scan has already passed today. Will not schedule for today.`
          );
        }
      }
    }

    // Schedule AI generation scan if enabled
    if (config?.geminiAiGeneration?.enabled) {
      if (usingCronSchedule) {
        const job = new CronJob(cronSchedule, () => {
          this.logger.log(`Running scheduled AI generation scan at ${new Date().toLocaleString()}...`);
          this.eventAiScannerService.scanEvents(false, []); // Adjust parameters as needed
        });
        this.schedulerRegistry.addCronJob('event-scan-today-ai-cron', job);
        job.start();
        this.logger.log(`AI generation scan job scheduled for today with cron schedule: ${cronSchedule}`);
      } else {
        const randomHour = Math.floor(Math.random() * 16) + 8; // 8 to 23
        const randomMinute = Math.floor(Math.random() * 60); // 0 to 59
        const scheduledTime = new Date(now.getFullYear(), now.getMonth(), now.getDate(), randomHour, randomMinute);

        // Only schedule if the random time hasn't passed today
        if (scheduledTime > now) {
          const jobName = `event-scan-today-ai-${todayString}`;
          const job = new CronJob(
            scheduledTime,
            () => {
              this.logger.log('Running scheduled AI generation scan...');
              this.eventAiScannerService.scanEvents(false, []); // Adjust parameters as needed
            },
            null,
            true
          );

          this.schedulerRegistry.addCronJob(jobName, job);
          const formattedMinute = randomMinute < 10 ? '0' + randomMinute : randomMinute;
          this.logger.log(`AI generation scan job scheduled for today at ${randomHour}:${formattedMinute}`);
        } else {
          const formattedMinute = randomMinute < 10 ? '0' + randomMinute : randomMinute;
          this.logger.log(
            `Random time ${randomHour}:${formattedMinute} for AI generation scan has already passed today. Will not schedule for today.`
          );
        }
      }
    }
  }

  /**
   * This method is now a placeholder, the actual scans are scheduled as one-time jobs
   * by scheduleScansBasedOnConfig.
   */
  async runEventScan() {
    this.logger.log('Main event scan trigger received. Scheduling daily scans...');
    this.scheduleScansBasedOnConfig();
  }

  /**
   * Run the event scan manually
   * @returns The result of the scan
   */
  async runManualScan() {
    this.logger.log('Running manual event scan...');
    const config = await this.botConfigService.getConfig();
    let resultEventBrite = [];
    let resultAiGeneration = [];

    if (config?.eventbrite?.enabled) {
      resultEventBrite = await this.eventScrapingScannerService.scanEventsByEventBrite();
    }

    if (config?.geminiAiGeneration?.enabled) {
      resultAiGeneration = await this.eventAiScannerService.scanEvents(false, []); // Adjust parameters as needed
    }

    return {
      resultEventBrite,
      resultAiGeneration
    };
  }
}
