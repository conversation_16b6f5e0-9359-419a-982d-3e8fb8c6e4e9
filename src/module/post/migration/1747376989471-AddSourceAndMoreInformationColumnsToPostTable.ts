import Column from '@core/database/schema/column';
import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddSourceAndMoreInformationColumnsToPostTable1747376989471 implements MigrationInterface {
  tableName = 'posts';

  public async up(queryRunner: QueryRunner): Promise<any> {
    if (await queryRunner.hasTable(this.tableName)) {
      if (!(await queryRunner.hasColumn(this.tableName, 'source'))) {
        await queryRunner.addColumns(this.tableName, [Column.string('source').nullable()]);
      }
      if (!(await queryRunner.hasColumn(this.tableName, 'more_information'))) {
        await queryRunner.addColumns(this.tableName, [Column.jsonb('more_information').nullable()]);
      }
    }
  }

  public async down(queryRunner: QueryRunner): Promise<any> {
    if (await queryRunner.hasColumn(this.tableName, 'source')) {
      await queryRunner.dropColumns(this.tableName, ['source']);
    }
    if (await queryRunner.hasColumn(this.tableName, 'more_information')) {
      await queryRunner.dropColumns(this.tableName, ['more_information']);
    }
  }
}
