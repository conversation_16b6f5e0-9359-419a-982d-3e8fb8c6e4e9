import Column from '@core/database/schema/column';
import { MigrationInterface, QueryRunner } from 'typeorm';
import { POST_STATUS } from '@module/post/const/post-status';

export class AddStatusColumnToPostTable1747643046759 implements MigrationInterface {
  tableName = 'posts';

  public async up(queryRunner: QueryRunner): Promise<any> {
    if (await queryRunner.hasTable(this.tableName)) {
      if (!(await queryRunner.hasColumn(this.tableName, 'status'))) {
        await queryRunner.addColumns(this.tableName, [
          Column.string('status').nullable().setDefault(POST_STATUS.activate)
        ]);
      }
    }
  }

  public async down(queryRunner: QueryRunner): Promise<any> {
    if (await queryRunner.hasColumn(this.tableName, 'status')) {
      await queryRunner.dropColumns(this.tableName, ['status']);
    }
  }
}
