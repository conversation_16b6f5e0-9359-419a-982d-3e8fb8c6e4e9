import { Controller, Post, Logger, UseGuards } from '@nestjs/common';
import { EventScannerJobService } from '@module/post/service/event-scanner-job.service';
import { IsSystemAdminGuard } from '@core/auth/guard/is-admin.guard';
import { AuthGuard } from '@nestjs/passport';

@UseGuards(AuthGuard(), IsSystemAdminGuard)
@Controller('/admin/event-scanner')
export class EventScannerController {
  private readonly logger = new Logger(EventScannerController.name);

  constructor(private readonly eventScannerJobService: EventScannerJobService) {}

  /**
   * Run a manual scan
   * @returns The result of the scan
   */
  @Post('scan')
  async runManualScan() {
    try {
      this.eventScannerJobService.runManualScan();

      return { message: 'The bot is scanning. Please check the result on CMS.' };
    } catch (error) {
      this.logger.error(`Error running manual scan: ${error.message}`, error.stack);

      return { error: 'Failed to run scan', message: error.message };
    }
  }
}
