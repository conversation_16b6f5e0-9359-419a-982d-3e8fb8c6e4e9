import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import axios from 'axios';

@Injectable()
export class GeminiService {
  private apiKey: string;
  private baseUrl = 'https://generativelanguage.googleapis.com/v1beta/models';
  private model = 'gemini-2.0-flash';

  constructor(private configService: ConfigService) {
    this.apiKey = this.configService.get<string>('gemini.apiKey');
    if (!this.apiKey) {
      console.warn('\x1b[33m%s\x1b[0m', 'GEMINI_API_KEY is not set. Gemini AI service will not work properly.');
    }
  }

  /**
   * Generate content using Gemini AI
   * @param prompt The prompt to send to Gemini AI
   * @returns The generated content
   */
  async generateContent(prompt: string): Promise<string> {
    try {
      const response = await axios.post(`${this.baseUrl}/${this.model}:generateContent?key=${this.apiKey}`, {
        contents: [
          {
            parts: [
              {
                text: prompt
              }
            ]
          }
        ],
        generationConfig: {
          temperature: 0.1
          // topK: 40,
          // topP: 0.95
          // maxOutputTokens: 1000
        }
      });

      // Extract the generated text from the response
      const generatedText = response.data.candidates[0].content.parts[0].text;

      return generatedText;
    } catch (error) {
      console.error('Error generating content with Gemini AI:', error.response?.data || error.message);
      throw new Error('Failed to generate content with Gemini AI');
    }
  }

  /**
   * Generate a post about a specific topic
   * @param topic The topic to generate a post about
   * @param location The location to associate with the post
   * @returns An object containing the title and message for the post
   */
  async generatePost(
    topic: string,
    location: { lat: string; long: string; address: string }
  ): Promise<{ title: string; message: string }> {
    const prompt = `
      Generate a social media post about ${topic}.
      The post should be informative, engaging, and suitable for a community platform.

      Format your response as follows:
      Title: [A catchy, concise title for the post, max 100 characters]

      [The main content of the post, 2-3 paragraphs, informative and engaging]
    `;

    const generatedContent = await this.generateContent(prompt);

    // Parse the generated content to extract title and message
    const titleMatch = generatedContent.match(/Title:\s*(.*?)(?:\n|$)/);
    const title = titleMatch ? titleMatch[1].trim() : `Interesting facts about ${topic}`;

    // Remove the title line to get just the message
    const message = generatedContent.replace(/Title:\s*(.*?)(?:\n|$)/, '').trim();

    return { title, message };
  }
}
