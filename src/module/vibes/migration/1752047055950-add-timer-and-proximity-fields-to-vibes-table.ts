import Column from '@core/database/schema/column';
import { MigrationInterface, QueryRunner } from 'typeorm';

export class addTimerAndProximityFieldsToVibesTable1752047055950 implements MigrationInterface {
  tableName = 'vibes';

  public async up(queryRunner: QueryRunner): Promise<any> {
    const columnsToAdd = [];

    if (!(await queryRunner.hasColumn(this.tableName, 'expired_at'))) {
      columnsToAdd.push(Column.timestamp('expired_at').nullable().note('Expiration date for the vibe'));
    }

    if (!(await queryRunner.hasColumn(this.tableName, 'timer_hours'))) {
      columnsToAdd.push(Column.integer('timer_hours').nullable().note('Timer duration in hours (2, 4, 8, etc.)'));
    }

    if (!(await queryRunner.hasColumn(this.tableName, 'proximity_lock'))) {
      columnsToAdd.push(
        Column.boolean('proximity_lock', false).note('Whether to restrict joining based on location proximity')
      );
    }

    if (!(await queryRunner.hasColumn(this.tableName, 'proximity_radius_km'))) {
      const radiusColumn = new Column({
        name: 'proximity_radius_km',
        type: 'decimal',
        precision: 10,
        scale: 2,
        isNullable: true,
        comment: 'Radius in kilometers for proximity lock'
      });
      columnsToAdd.push(radiusColumn);
    }

    if (columnsToAdd.length > 0) {
      await queryRunner.addColumns(this.tableName, columnsToAdd);
    }
  }

  public async down(queryRunner: QueryRunner): Promise<any> {
    if (await queryRunner.hasColumn(this.tableName, 'expired_at')) {
      await queryRunner.dropColumn(this.tableName, 'expired_at');
    }

    if (await queryRunner.hasColumn(this.tableName, 'proximity_radius_km')) {
      await queryRunner.dropColumn(this.tableName, 'proximity_radius_km');
    }

    if (await queryRunner.hasColumn(this.tableName, 'proximity_lock')) {
      await queryRunner.dropColumn(this.tableName, 'proximity_lock');
    }

    if (await queryRunner.hasColumn(this.tableName, 'timer_hours')) {
      await queryRunner.dropColumn(this.tableName, 'timer_hours');
    }
  }
}
