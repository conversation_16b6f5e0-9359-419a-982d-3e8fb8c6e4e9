import Column from '@core/database/schema/column';
import { MigrationInterface, QueryRunner } from 'typeorm';

export class addPinnedFieldsToVibesTable1752051694526 implements MigrationInterface {
  tableName = 'vibes';

  public async up(queryRunner: QueryRunner): Promise<any> {
    const columnsToAdd = [];

    if (!(await queryRunner.hasColumn(this.tableName, 'is_pin'))) {
      columnsToAdd.push(Column.boolean('is_pin', false).note('Whether this vibe is pinned by admin'));
    }

    if (!(await queryRunner.hasColumn(this.tableName, 'pin_order'))) {
      columnsToAdd.push(
        Column.integer('pin_order').nullable().note('Order of pinned vibes (lower numbers appear first)')
      );
    }

    if (columnsToAdd.length > 0) {
      await queryRunner.addColumns(this.tableName, columnsToAdd);
    }
  }

  public async down(queryRunner: QueryRunner): Promise<any> {
    if (await queryRunner.hasColumn(this.tableName, 'pin_order')) {
      await queryRunner.dropColumn(this.tableName, 'pin_order');
    }

    if (await queryRunner.hasColumn(this.tableName, 'is_pin')) {
      await queryRunner.dropColumn(this.tableName, 'is_pin');
    }
  }
}
