import {
  IsBoolean,
  IsDateString,
  IsIn,
  IsInt,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsPositive,
  IsString,
  IsUUID,
  <PERSON>Length,
  ValidateNested
} from 'class-validator';
import { BaseRequestDto } from '@core/support/validation/base-request-dto';
import { DEFAULT_PROXIMITY_RADIUS_KM, VIBE_TYPE } from '../const';
import { STRING_LONG_LENGTH, STRING_LENGTH } from '@core/database/schema/column';
import { TagEntity } from '@module/tag/entity/tag.entity';
import { TAG_STATUS } from '@module/tag/const/status';
import { BadRequestException } from '@nestjs/common';
import { LocationDto } from '@core/validation/location.dto';
import { Type } from 'class-transformer';

export class CreateVibeDto extends BaseRequestDto {
  @IsString()
  @IsNotEmpty()
  @MaxLength(STRING_LENGTH)
  name: string;

  @IsString()
  @IsOptional()
  @MaxLength(STRING_LONG_LENGTH)
  description: string;

  @IsOptional()
  @IsIn(Object.values(VIBE_TYPE))
  type: VIBE_TYPE;

  @IsOptional()
  @IsUUID()
  tagId: string;

  @IsOptional()
  @ValidateNested()
  @Type(() => LocationDto)
  location: LocationDto;

  @IsOptional()
  @IsNumber()
  @IsPositive()
  @IsInt()
  timerHours: number;

  @IsOptional()
  @IsDateString()
  expiredAt: Date;

  @IsOptional()
  @IsBoolean()
  proximityLock: boolean;

  @IsOptional()
  @IsNumber()
  @IsPositive()
  proximityRadiusKm: number;

  async extraValidation() {
    if (this.tagId) {
      const tag = await TagEntity.findOne({ where: { id: this.tagId, status: TAG_STATUS.active } });

      if (!tag) {
        throw new BadRequestException('TAG001');
      }
    }
    if (!this.proximityLock) {
      this.proximityRadiusKm = null;
    }

    if (this.proximityLock && !this.proximityRadiusKm) {
      this.proximityRadiusKm = DEFAULT_PROXIMITY_RADIUS_KM;
    }
  }
}
