import { Injectable, BadRequestException } from '@nestjs/common';
import { Brackets, DeepPartial, SelectQueryBuilder } from 'typeorm';
import { SearchVibeDto } from '@module/vibes/validation/search-vibe.dto';
import { BaseDatabaseService } from '@core/service/base.service';
import { VibeEntity } from '@module/vibes/entity/vibe.entity';
import { PointTransformer } from '@core/database/type/point';
import { UserEntity } from '@core/user/entity/user.entity';
import { VibeUserEntity } from '@module/vibes/entity/vibe-user.entity';
import { VibeCommentEntity } from '@module/vibes/entity/vibe-comment.entity';
import { LocationDto } from '@core/validation/location.dto';
import { DEFAULT_PROXIMITY_RADIUS_KM } from '@module/vibes/const';
import * as dayjs from 'dayjs';

@Injectable()
export class VibeService extends BaseDatabaseService {
  constructor() {
    super();
  }

  searchQuery(searchVibeDto: SearchVibeDto): SelectQueryBuilder<VibeEntity> {
    const queryBuilder = VibeEntity.createQueryBuilder('vibe');

    // Filter out expired vibes
    queryBuilder.where('(vibe.expiredAt IS NULL OR vibe.expiredAt > :currentTime)', {
      currentTime: new Date()
    });

    if (searchVibeDto.key) {
      queryBuilder.andWhere(
        new Brackets(qb => {
          qb.where('vibe.name ILIKE :value').orWhere('vibe.description ILIKE :value', {
            value: `%${searchVibeDto.key}%`
          });
        })
      );
    }

    // Always prioritize pinned vibes first
    queryBuilder.addOrderBy('vibe.isPin', 'DESC'); // Pinned vibes first (true > false)
    queryBuilder.addOrderBy('vibe.pinOrder', 'ASC', 'NULLS LAST'); // Then by pin order

    if (searchVibeDto.orderBy !== 'last_activity') {
      queryBuilder.addOrderBy(`vibe.${searchVibeDto.orderBy}`, searchVibeDto.orderType);
    }

    return queryBuilder;
  }

  async getVibes(searchVibeDto: SearchVibeDto, user?: UserEntity) {
    const searchQuery = this.searchQuery(searchVibeDto);
    this.leftJoinCreatedByUser(searchQuery);
    this.leftJoinTag(searchQuery);

    if (user) {
      searchQuery.leftJoinAndMapOne(
        'vibe.vibeUser',
        VibeUserEntity,
        'vibe_user',
        '(vibe_user.vibe_id = vibe.id AND vibe_user.user_id = :userId)',
        { userId: user.id }
      );
    }

    searchQuery.loadRelationCountAndMap('vibe.countVibeComments', 'vibe.vibeComments', 'count_vibe_comment');

    const subQuery = searchQuery
      .subQuery()
      .select('vibe_cmt.id')
      .from(VibeCommentEntity, 'vibe_cmt')
      .where(`vibe_cmt.vibeId = vibe.id `)
      .orderBy('vibe_cmt.createdAt', 'DESC')
      .limit(3)
      .getQuery();

    searchQuery.leftJoinAndMapMany(
      'vibe.vibeComments',
      VibeCommentEntity,
      'latest_vibe_comment',
      `latest_vibe_comment.id in (${subQuery})`
    );

    searchQuery
      .leftJoin('latest_vibe_comment.createdByUser', 'latest_vibe_comment_user')
      .leftJoin('latest_vibe_comment_user.profile', 'latest_vibe_comment_user_prof')
      .addSelect([
        'latest_vibe_comment_user.id',
        'latest_vibe_comment_user.email',
        'latest_vibe_comment_user.phoneNumber',
        'latest_vibe_comment_user.username',
        'latest_vibe_comment_user_prof.firstName',
        'latest_vibe_comment_user_prof.lastName',
        'latest_vibe_comment_user_prof.avatar',
        'latest_vibe_comment_user_prof.mascotAvatar'
      ]);

    if (searchVibeDto.lat && searchVibeDto.long) {
      // Use PointTransformer.getQuerySqlDistanceLocation for robust SQL
      const distanceSql = PointTransformer.getQuerySqlDistanceLocation(
        'vibe.location[0]', // lat in db
        'vibe.location[1]', // long in db
        searchVibeDto.lat,
        searchVibeDto.long
      );
      searchQuery.addSelect(distanceSql, 'vibe_distance');
      // Clear existing order and prioritize pinned vibes, then distance
      searchQuery.orderBy('vibe.isPin', 'DESC');
      searchQuery.addOrderBy('vibe.pinOrder', 'ASC', 'NULLS LAST');
      searchQuery.addOrderBy('vibe_distance', 'ASC', 'NULLS LAST');
    } else if (searchVibeDto.orderBy === 'last_activity') {
      // Clear existing order and prioritize pinned vibes, then last activity
      searchQuery.orderBy('vibe.isPin', 'DESC');
      searchQuery.addOrderBy('vibe.pinOrder', 'ASC', 'NULLS LAST');
      searchQuery.addOrderBy('latest_vibe_comment.createdAt', searchVibeDto.orderType, 'NULLS LAST');
    }

    const paginationOptions = this.getPaginationOptions(searchVibeDto);

    return this.paginate(searchQuery, paginationOptions);
  }

  private leftJoinTag(queryBuilder: SelectQueryBuilder<VibeEntity>) {
    queryBuilder.leftJoin('vibe.tag', 'tag').addSelect(['tag.id', 'tag.icon', 'tag.tag', 'tag.types']);

    return queryBuilder;
  }

  private leftJoinCreatedByUser(queryBuilder: SelectQueryBuilder<VibeEntity>) {
    queryBuilder
      .leftJoin('vibe.createdByUser', 'crt_by_user')
      .leftJoin('crt_by_user.profile', 'crt_by_user_prof')
      .addSelect([
        'crt_by_user.id',
        'crt_by_user.email',
        'crt_by_user.phoneNumber',
        'crt_by_user.username',
        'crt_by_user_prof.firstName',
        'crt_by_user_prof.lastName',
        'crt_by_user_prof.avatar',
        'crt_by_user_prof.mascotAvatar'
      ]);

    return queryBuilder;
  }

  createVibe(data: DeepPartial<VibeEntity>) {
    // Handle expiredAt and timerHours logic
    if (data.expiredAt && !data.timerHours) {
      // If expiredAt is provided but timerHours is not, calculate timerHours
      const now = new Date();
      const expiredAt = new Date(data.expiredAt as any);
      const timeDiffMs = expiredAt.getTime() - now.getTime();
      const timerHours = Math.round(timeDiffMs / (60 * 60 * 1000));
      data.timerHours = timerHours > 0 ? timerHours : null;
    } else if (data.timerHours && !data.expiredAt) {
      // If timerHours is provided but expiredAt is not, calculate expiredAt (backward compatibility)
      const now = new Date();
      const expiredAt = new Date(now.getTime() + data.timerHours * 60 * 60 * 1000);
      data.expiredAt = expiredAt;
    }

    // If proximityLock is enabled but proximityRadiusKm is not set, use default value
    if (data.proximityLock && !data.proximityRadiusKm) {
      data.proximityRadiusKm = DEFAULT_PROXIMITY_RADIUS_KM;
    }

    return VibeEntity.create<VibeEntity>(data).save();
  }

  async updateVibe(id: string, data: DeepPartial<VibeEntity>) {
    // Handle expiredAt and timerHours logic
    if (data.expiredAt !== undefined) {
      if (data.expiredAt) {
        // If expiredAt is being set, calculate timerHours
        const now = new Date();
        const expiredAt = new Date(data.expiredAt as any);
        const timeDiffMs = expiredAt.getTime() - now.getTime();
        const timerHours = Math.round(timeDiffMs / (60 * 60 * 1000));
        data.timerHours = timerHours > 0 ? timerHours : null;
      } else {
        // If expiredAt is being cleared, also clear timerHours
        data.timerHours = null;
      }
    } else if (data.timerHours !== undefined) {
      // Backward compatibility: if only timerHours is provided, calculate expiredAt
      if (data.timerHours) {
        const now = new Date();
        const expiredAt = new Date(now.getTime() + data.timerHours * 60 * 60 * 1000);
        data.expiredAt = expiredAt;
      } else {
        // If timerHours is being cleared, also clear expiredAt
        data.expiredAt = null;
      }
    }

    // If proximityLock is enabled but proximityRadiusKm is not set, use default value
    if (data.proximityLock && !data.proximityRadiusKm) {
      data.proximityRadiusKm = DEFAULT_PROXIMITY_RADIUS_KM;
    }

    await VibeEntity.update(id, data);

    return VibeEntity.findOne({ where: { id } });
  }

  /**
   * Check if a user can join a vibe based on proximity lock settings
   */
  async checkProximityAccess(vibe: VibeEntity, userLocation?: LocationDto): Promise<void> {
    // check vibe expired
    if (vibe.expiredAt && dayjs(new Date()).isAfter(dayjs(vibe.expiredAt))) {
      throw new BadRequestException('VIBE005');
    }

    // check vibe expired
    if (vibe.expiredAt > vibe.expiredAt) {
      throw new BadRequestException('VIBE005');
    }

    // If proximity lock is disabled, allow access
    if (!vibe.proximityLock) {
      return;
    }

    // If proximity lock is enabled but no user location provided, deny access
    if (!userLocation?.lat || !userLocation?.long) {
      throw new BadRequestException('VIBE003');
    }

    // If vibe has no location, deny access (shouldn't happen but safety check)
    if (!vibe.location?.lat || !vibe.location?.long) {
      throw new BadRequestException('VIBE004');
    }

    // Calculate distance between user and vibe location
    const distance = PointTransformer.distanceBetween2Points(
      { lat: userLocation.lat, long: userLocation.long },
      { lat: vibe.location.lat.toString(), long: vibe.location.long.toString() },
      'km'
    );

    // Check if user is within the allowed radius
    if (distance > vibe.proximityRadiusKm) {
      throw new BadRequestException(`You must be within ${vibe.proximityRadiusKm}km of the vibe location to join`);
    }
  }
}
