import { NotificationEntity } from '@module/notification/entity/notification.entity';
import { handleResponseUser } from './utils/handle-response-user-info';
import { Components } from '../template/component';
import { NOTIFICATION_TYPE } from '@module/notification/const/notification-type';
import { NOTIFICATION_STATUS } from '@module/notification/const/notification-status';
import { NOTIFICATION_KEY } from '@module/notification/const/notification-key';
import { PushingNotificationService } from '@module/notification/service/pushing-notification.service';
import { UserEntity } from '@core/user/entity/user.entity';
import { Logging } from '@core/logger/logging.service';

export const notificationResource = {
  resource: NotificationEntity,
  options: {
    navigation: {
      name: 'Notification Management'
    },
    sort: {
      direction: 'desc',
      sortBy: 'createdAt'
    },
    listProperties: [
      'title',
      'body',
      'type',
      'key',
      'status',
      'description',
      'userId',
      'referenceEntity',
      'createdAt',
      'createdBy'
    ],
    showProperties: [
      'id',
      'title',
      'body',
      'type',
      'key',
      'status',
      'description',
      'userId',
      'messageId',
      'referenceEntity',
      'referenceId',
      'deliveredAt',
      'readAt',
      'createdAt',
      'updatedAt',
      'createdBy'
    ],
    properties: {
      type: {
        type: 'string',
        isRequired: true,
        props: {
          NOTIFICATION_TYPE
        },
        availableValues: Object.values(NOTIFICATION_TYPE).map(type => ({
          value: type,
          label: type
        }))
      },
      key: {
        type: 'string'
      },
      status: {
        type: 'string',
        isRequired: true,
        availableValues: Object.values(NOTIFICATION_STATUS).map(status => ({
          value: status,
          label: status
        }))
      },
      notification: {
        type: 'mixed',
        isRequired: true,
        components: {}
      },
      title: {
        type: 'string'
      },
      body: {
        type: 'string'
      }
    },
    actions: {
      list: {
        isVisible: true,
        after: res => {
          if (res.records?.length) {
            res.records = res.records.map(r => {
              r.params.title = r.params?.['notification.title'];
              r.params.body = r.params?.['notification.body'];
              if (typeof r.populated?.userId === 'string') {
                r.populated.userId = {
                  id: r.populated?.userId,
                  recordActions: []
                };
              } else {
                r.populated.userId = handleResponseUser(r.populated.userId);
              }

              if (typeof r.populated?.createdBy === 'string') {
                r.populated.createdBy = {
                  id: r.populated?.createdBy,
                  recordActions: []
                };
              } else {
                r.populated.createdBy = handleResponseUser(r.populated.createdBy);
              }

              return r;
            });
          }

          return res;
        }
      },
      show: {
        isVisible: true,
        after: async res => {
          res.record.params.title = res.record.params?.['notification.title'];
          res.record.params.body = res.record.params?.['notification.body'];
          if (typeof res.record.populated?.userId === 'string') {
            res.record.populated.userId = {
              id: res.record.populated?.userId,
              recordActions: []
            };
          } else {
            res.record.populated.userId = handleResponseUser(res.record.populated.userId);
          }

          if (typeof res.record.populated?.createdBy === 'string') {
            res.record.populated.createdBy = {
              id: res.record.populated?.createdBy,
              recordActions: []
            };
          } else {
            res.record.populated.createdBy = handleResponseUser(res.record.populated.createdBy);
          }

          return res;
        }
      },
      new: {
        component: Components.CreateNotification
      },
      edit: {
        isVisible: false,
        isAccessible: false
      },
      delete: {
        isVisible: true
      },
      'send-notification': {
        isVisible: false,
        actionType: 'resource',
        hideActionHeader: true,
        handler: async (request, response) => {
          try {
            const payload = request.payload;
            const { session } = request;
            const createdBy = session?.adminUser?.id;
            const notificationData = {
              createdBy,
              description: payload.data?.description,
              key: NOTIFICATION_KEY.adminSendNotification,
              referenceId: payload.data?.referenceId,
              referenceEntity: payload.data?.referenceEntity
            };

            let toUserIds = [];
            if (payload.sendToAllUsers) {
              // re handle for case send to all users | using general topic?
              toUserIds = await UserEntity.createQueryBuilder('user')
                .select('user.id')
                .where('user.deletedAt IS NULL')
                .andWhere('(user.username != :username OR user.isDummy = true)', { username: 'comun1ty_event' })
                .getMany()
                .then(users => users.map(user => user.id));
            } else {
              toUserIds = payload.userIds as string[];
            }

            if (!toUserIds.length) {
              return response.send({ success: false, error: 'No user selected' }).status(400);
            }

            PushingNotificationService.sendMultiNotification({
              toUserIds: toUserIds,
              notification: payload.notification,
              data: { ...payload.data, key: NOTIFICATION_KEY.adminSendNotification },
              notificationData
            });

            return { success: true };
          } catch (error) {
            Logging.error('Error while sending message', error);

            return response.send({ success: false, error: error.message }).status(400);
          }
        }
      }
    }
  }
};
