import { UserProfileEntity } from '@core/user/entity/user-profile.entity';
import { UserEntity } from '@core/user/entity/user.entity';
import { NeighborhoodMemberEntity } from '@module/neighborhood/entity/neighborhood-member.entity';
import { NeighborhoodEntity } from '@module/neighborhood/entity/neighborhood.entity';
import { unflatten, ValidationError } from 'adminjs';
import { isLatitude, isLongitude } from 'class-validator';
import { handleResponseUser } from './utils/handle-response-user-info';
export const userResource = {
  resource: UserEntity,
  options: {
    navigation: {
      name: 'User Management'
    },
    sort: {
      direction: 'desc',
      sortBy: 'createdAt'
    },
    properties: {
      firstName: {
        type: 'string'
      },
      lastName: {
        type: 'string'
      },
      avatar: {
        type: 'string'
      },
      postalCode: {
        type: 'string'
      },
      profile: {
        reference: 'UserProfileEntity'
      },
      location: {
        type: 'mixed'
      },
      'location.lat': {
        type: 'string'
      },
      'location.long': {
        type: 'string'
      },
      districtName: {
        type: 'string'
      }
    },
    listProperties: [
      'email',
      'phoneNumber',
      'username',
      'firstName',
      'lastName',
      'postalCode',
      'districtName',
      'activated',
      'status',
      'isVerifiedEmail',
      'isVerifiedPhoneNumber',
      'createdAt'
    ],
    filterProperties: ['email', 'phoneNumber', 'username', 'status'],
    editProperties: [
      'email',
      'phoneNumber',
      'username',
      'firstName',
      'lastName',
      'status',
      'postalCode',
      'location',
      'districtName'
    ],
    showProperties: [
      'username',
      'email',
      'phoneNumber',
      'firstName',
      'lastName',
      'status',
      'activated',
      'postalCode',
      'districtName',
      'location',
      'isVerifiedEmail',
      'isVerifiedPhoneNumber',
      'isKYC',
      'createdAt',
      'updatedAt'
    ],
    actions: {
      new: {
        isVisible: false
      },
      edit: {
        handler: async (request, response, context) => {
          const method = request.method;
          if (method === 'get') {
            const record = context.record;
            const userRecord = record.toJSON(context.currentAdmin);
            const nbhMember = await NeighborhoodMemberEntity.findOne({
              where: { userId: userRecord.params.id },
              relations: ['neighborhood'],
              order: { updatedAt: 'DESC' }
            });
            if (nbhMember?.neighborhood) {
              userRecord.params.districtName = nbhMember.neighborhood.name;
              userRecord.params.neighborhoodId = nbhMember.neighborhood.id;
            }
            userRecord.params.postalCode = userRecord.params['profile.postalCode'];
            userRecord.params['location.lat'] = userRecord.params['profile.location.lat'];
            userRecord.params['location.long'] = userRecord.params['profile.location.long'];
            userRecord.params.avatar = userRecord.params['profile.avatar'];
            userRecord.params.firstName = userRecord.params['profile.firstName'];
            userRecord.params.lastName = userRecord.params['profile.lastName'];
            delete userRecord.params.password;
            delete userRecord.params.salt;
            delete userRecord.params.forceChangePassword;
            delete userRecord.params.resetPasswordToken;

            return { record: userRecord };
          }
          const { payload } = request;
          const { record, h, resource, translateMessage } = context;
          if (!Object.keys(payload).length) return { record: record.toJSON() };

          Object.keys(payload).forEach(key => {
            if (key.includes('profile') || key.includes('origin')) {
              delete payload[key];
            }
          });
          const {
            id: userId,
            firstName,
            lastName,
            avatar,
            postalCode,
            districtName,
            neighborhoodId,
            ...others
          } = payload;
          let updateProfile: Record<string, any> = {
            firstName,
            lastName,
            avatar,
            postalCode
          };
          updateProfile['location.lat'] = others['location.lat'];
          updateProfile['location.long'] = others['location.long'];
          delete others['location.lat'];
          delete others['location.long'];
          const updateNeighborhood = {
            districtName,
            neighborhoodId
          };
          updateProfile = unflatten(updateProfile);

          if (updateProfile.location?.lat || updateProfile.location?.long) {
            if (!isLatitude(updateProfile.location?.lat)) {
              throw new ValidationError(
                {
                  lat: {
                    message: 'location lat invalid'
                  }
                },
                {
                  message: 'Latitude invalid'
                }
              );
            }
            if (!isLongitude(updateProfile.location?.long)) {
              throw new ValidationError(
                {
                  long: {
                    message: 'location long invalid'
                  }
                },
                {
                  message: 'Longitude invalid'
                }
              );
            }
          }
          let neighborhoodEntity;
          if (updateNeighborhood.districtName) {
            neighborhoodEntity = await NeighborhoodEntity.findOne({
              where: { name: updateNeighborhood.districtName }
            });
            if (!neighborhoodEntity) {
              throw new ValidationError(
                {
                  long: {
                    message: 'District not found'
                  }
                },
                {
                  message: 'District not found'
                }
              );
            }
          }

          await UserEntity.update({ id: userId }, others);

          // update user profile
          const userProfile = await UserProfileEntity.findOne({ where: { userId } });
          if (userProfile) {
            if (updateProfile.location.lat && updateProfile.location.long) {
              if (!userProfile.locations) {
                userProfile.locations = [];
              }
              const existLocation = userProfile.locations.find(
                lct => lct.lat === updateProfile.location.lat && lct.long === updateProfile.location.long
              );
              // if not exist new location then will push to locations history
              if (!existLocation) {
                userProfile.locations.push(updateProfile.location);
              }
            }

            Object.assign(userProfile, updateProfile);
            await userProfile.save();
          }
          // if changed district name, will re-update neighborhood for member
          if (neighborhoodEntity) {
            const nbhMember = await NeighborhoodMemberEntity.findOne({
              where: { userId },
              order: { updatedAt: 'DESC' }
            });
            if (!nbhMember) {
              await NeighborhoodMemberEntity.create<NeighborhoodMemberEntity>({
                userId,
                neighborhoodId: neighborhoodEntity.id
              }).save();
            } else {
              nbhMember.neighborhoodId = neighborhoodEntity.id;
              await nbhMember.save();
            }
          }

          record.params = payload;

          return {
            record: record.toJSON(),
            notice: {
              message: translateMessage('successfullyUpdated', resource.id()),
              type: 'success'
            },
            redirectUrl: h.resourceUrl({ resourceId: resource._decorated?.id() || resource.id() })
          };
        }
      },
      delete: {
        isVisible: false
      },
      list: {
        after: async res => {
          if (res.records?.length) {
            res.records = await Promise.all(
              res.records.map(async r => {
                r.params.firstName = r.params['profile.firstName'];
                r.params.lastName = r.params['profile.lastName'];
                r.params.postalCode = r.params['profile.postalCode'];
                const nbhMember = await NeighborhoodMemberEntity.findOne({
                  where: { userId: r.params.id },
                  relations: ['neighborhood'],
                  order: { updatedAt: 'DESC' }
                });
                if (nbhMember?.neighborhood) {
                  r.params.districtName = nbhMember?.neighborhood.name;
                }
                delete r.params.password;
                delete r.params.salt;
                delete r.params.forceChangePassword;
                delete r.params.resetPasswordToken;

                return r;
              })
            );
          }

          return res;
        }
      },
      show: {
        after: async res => {
          if (res.record.params) {
            const nbhMember = await NeighborhoodMemberEntity.findOne({
              where: { userId: res.record.params.id },
              relations: ['neighborhood']
            });
            if (nbhMember?.neighborhood) {
              res.record.params.districtName = nbhMember.neighborhood.name;
              res.record.params.neighborhoodId = nbhMember.neighborhood.id;
            }
            res.record.params.postalCode = res.record.params['profile.postalCode'];
            res.record.params['location.lat'] = res.record.params['profile.location.lat'];
            res.record.params['location.long'] = res.record.params['profile.location.long'];
            res.record.params.avatar = res.record.params['profile.avatar'];
            res.record.params.firstName = res.record.params['profile.firstName'];
            res.record.params.lastName = res.record.params['profile.lastName'];
            delete res.record.params.password;
            delete res.record.params.salt;
            delete res.record.params.forceChangePassword;
            delete res.record.params.resetPasswordToken;
          }

          return res;
        }
      },
      search: {
        after: async res => {
          if (res.records?.length) {
            res.records.map(async r => {
              r.params.firstName = r.params['profile.firstName'];
              r.params.lastName = r.params['profile.lastName'];
              r.params.postalCode = r.params['profile.postalCode'];
              r = handleResponseUser(r);

              return r;
            });
          }

          return res;
        }
      }
    }
  }
};
