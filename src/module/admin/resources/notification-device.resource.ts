import { DeviceEntity } from '@module/device/entity/device.entity';
export const notificationDeviceResource = {
  resource: DeviceEntity,
  options: {
    navigation: {
      name: 'Notification Management'
    },
    actions: {
      list: {
        isVisible: false,
        isAccessible: false
      },
      show: {
        isVisible: false,
        isAccessible: false
      },
      new: {
        isVisible: false,
        isAccessible: false
      },
      edit: {
        isVisible: false,
        isAccessible: false
      },
      delete: {
        isAccessible: false
      },
      bulkDelete: {
        isAccessible: false
      }
    }
  }
};
