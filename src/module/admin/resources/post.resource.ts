/* eslint-disable max-len */
import { PostEntity } from '@module/post/entity/post.entity';
import AdminJS, { ValidationError, flatten, unflatten } from 'adminjs';
import { isLatitude, isLongitude } from 'class-validator';
import { POST_TYPE } from '@module/post/const/post-type';
import { handleResponseUser } from './utils/handle-response-user-info';
import { NeighborhoodMemberEntity } from '@module/neighborhood/entity/neighborhood-member.entity';
import { MerchantEntity } from '@module/merchant/entity/merchant.entity';
import * as XLSX from 'xlsx';
import { FileValidator } from '@core/support/file';
import { UserEntity } from '@core/user/entity/user.entity';
import { In, Brackets } from 'typeorm';
import { NeighborhoodEntity } from '@module/neighborhood/entity/neighborhood.entity';
import { PostNeighborhoodPlaceService } from '@module/post/service/post-neighborhood-place.service';
import { Components } from '../template/component';
import { getImageBuffer } from './utils/get-image-buffer';
import S3Driver from '@core/storage/driver/s3.driver';
import * as jszip from 'jszip';
import * as fs from 'fs';
import * as xml2js from 'xml2js';
// import { PlaceEntity } from '@module/place/entity/place.entity';
import { PostPlaceService } from '@module/post/service/post-place.service';
import { PostPlaceEntity } from '@module/post/entity/post-place.entity';
import { PlaceService } from '@module/place/service/place.service';
import { uploadImageFeature } from './utils/upload-feature';
import { PlaceEntity } from '@module/place/entity/place.entity';
import { POST_STATUS } from '@module/post/const/post-status';
import { POST_SOURCE } from '@module/post/const/post-source';
import { BaseDatabaseService } from '@core/service/base.service';
import { BaseSearchDto } from '@core/support/validation/base-search-dto';

type PostImport = {
  username: string;
  'neighborhood code': string;
  title?: string;
  message?: string;
  photos?: string;
  type: string;
  tags?: string;
  location?: string;
  row?: number;
};

const validateLocation = (location: { lat?: string; long?: string }) => {
  if (!isLatitude(location?.lat)) {
    throw new ValidationError(
      {
        'location.lat': {
          message: 'Latitude invalid'
        }
      },
      {
        message: 'Latitude invalid'
      }
    );
  }
  if (!isLongitude(location?.long)) {
    throw new ValidationError(
      {
        'location.long': {
          message: 'Longitude invalid'
        }
      },
      {
        message: 'Longitude invalid'
      }
    );
  }
};

/**
 *
 * @param req Request
 * @returns
 */
const validateNewAndEditPost = async req => {
  const payload = req.payload;

  const payloadUnflatten = unflatten(payload || {});
  // only groupId or neighborhoodId added to post
  // if (!payload.groupId && !payload.neighborhoodId) {
  //   throw new ValidationError(
  //     {
  //       groupOrNeighborhood: {
  //         message: 'Group or Neighborhood is required'
  //       }
  //     },
  //     {
  //       message: 'Group or Neighborhood is required'
  //     }
  //   );
  // }
  // if (payload.groupId && payload.neighborhoodId) {
  //   throw new ValidationError(
  //     {
  //       groupOrNeighborhood: {
  //         message: 'A post cannot be simultaneously assigned to a group and a neighborhood.'
  //       }
  //     },
  //     {
  //       message: 'A post cannot be simultaneously assigned to a group and a neighborhood.'
  //     }
  //   );
  // }

  // only createdBy or createdByMerchantId added to post
  if (!payloadUnflatten.createdBy && !payloadUnflatten.createdByMerchantId) {
    throw new ValidationError(
      {
        createdByUserCreatedByMerchant: {
          message: 'Created by User or Merchant is required'
        }
      },
      {
        message: 'Created by User or Merchant is required'
      }
    );
  }
  if (payloadUnflatten.createdBy && payloadUnflatten.createdByMerchantId) {
    throw new ValidationError(
      {
        groupOrNeighborhood: {
          message: 'A post cannot be simultaneously assigned to a Created by User and a Created by Merchant.'
        }
      },
      {
        message: 'A post cannot be simultaneously assigned to a Created by User and a Created by Merchant.'
      }
    );
  }

  if (!payloadUnflatten.type) {
    throw new ValidationError(
      {
        type: {
          message: 'Type is required'
        }
      },
      {
        message: 'Type is required'
      }
    );
  }
  if (!payloadUnflatten.message) {
    throw new ValidationError(
      {
        message: {
          message: 'Message is required'
        }
      },
      {
        message: 'Message is required'
      }
    );
  }
  const files = unflatten(req.files || {});
  if ([POST_TYPE.invite, POST_TYPE.hiddenGem].includes(payloadUnflatten.type)) {
    if (payloadUnflatten.createdByMerchantId) {
      throw new ValidationError(
        {
          createdByMerchantId: {
            message: `Created by Merchant cannot add ${POST_TYPE.invite} or ${POST_TYPE.hiddenGem} type`
          }
        },
        {
          message: `Created by Merchant cannot add ${POST_TYPE.invite} or ${POST_TYPE.hiddenGem} type`
        }
      );
    }
    if (!files?.Photos?.length && !payloadUnflatten.photos?.length) {
      throw new ValidationError(
        {
          photos: {
            message: 'Photos is required'
          }
        },
        {
          message: 'Photos is required'
        }
      );
    }
  }

  if (payloadUnflatten.type === POST_TYPE.invite) {
    if (!payloadUnflatten.title) {
      throw new ValidationError(
        {
          title: {
            message: 'Title is required'
          }
        },
        {
          message: 'Title is required'
        }
      );
    }
    // if (!payloadUnflatten.address) {
    //   throw new ValidationError(
    //     {
    //       address: {
    //         message: 'Address is required'
    //       }
    //     },
    //     {
    //       message: 'Address is required'
    //     }
    //   );
    // }
    if (!payloadUnflatten.startDate) {
      throw new ValidationError(
        {
          startDate: {
            message: 'Start date is required'
          }
        },
        {
          message: 'Start date is required'
        }
      );
    }
    if (!payloadUnflatten.endDate) {
      throw new ValidationError(
        {
          endDate: {
            message: 'End date is required'
          }
        },
        {
          message: 'End date is required'
        }
      );
    }
  }

  if (payloadUnflatten.type === POST_TYPE.hiddenGem) {
    if (!payloadUnflatten.tags?.length) {
      throw new ValidationError(
        {
          tags: {
            message: 'Tags is required'
          }
        },
        {
          message: 'Tags is required'
        }
      );
    }
  }

  if (payloadUnflatten.isPin !== 'true' && payloadUnflatten.isPin !== true) {
    payloadUnflatten.pinOrder = null;
  }
  if (payloadUnflatten.createdBy && !payloadUnflatten.groupId) {
    const neighborhoodMb = await NeighborhoodMemberEntity.findOne({
      where: { userId: payloadUnflatten.createdBy },
      order: { createdAt: 'DESC' }
    });
    payloadUnflatten.neighborhoodId = neighborhoodMb?.neighborhoodId;
  } else if (payloadUnflatten.createdByMerchantId) {
    payloadUnflatten.merchantId = payloadUnflatten.createdByMerchantId;
    payloadUnflatten.groupId = null;
    const merchant = await MerchantEntity.findOne({ where: { id: payloadUnflatten.createdByMerchantId } });
    payloadUnflatten.neighborhoodId = merchant?.neighborhoodId;
  }

  // priority location over placeId
  // if location changed, => get auto place based on location

  const params = req.params;
  if (params?.action === 'edit') {
    const originRecordBeforeUpdated = payloadUnflatten.origin;

    if (
      (payloadUnflatten?.location?.lat || payloadUnflatten?.location?.long) &&
      (originRecordBeforeUpdated?.location?.lat !== payloadUnflatten.location.lat ||
        originRecordBeforeUpdated?.location?.long !== payloadUnflatten.location.long)
    ) {
      validateLocation(payloadUnflatten.location);

      const place = (await PostPlaceService.saveAndGetNearbyPlaceByLocation(
        {
          location: payloadUnflatten.location
        },
        { returnValue: 'placeObject' }
      )) as PlaceEntity;

      if (place) {
        payloadUnflatten.placeId = place.id;
        payloadUnflatten.address = place.address;
      }
    } else if (payloadUnflatten.placeId) {
      const oldPostPlace = await PostPlaceEntity.findOne({ where: { postId: payloadUnflatten.id } });

      // re update infor if place id was changed
      if (payloadUnflatten.placeId !== oldPostPlace?.placeId) {
        const place = await PlaceService.getPlaceDetailOrSaveDataIfMissingDataByPlaceId(payloadUnflatten.placeId);
        if (!place) {
          throw new ValidationError(
            {
              placeId: {
                message: 'Place not found'
              }
            },
            {
              message: 'Place not found'
            }
          );
        }

        payloadUnflatten.address = place.address;
        payloadUnflatten.location = place.location;
      }
    }
  } else {
    if (payloadUnflatten.location?.lat || payloadUnflatten.location?.long) {
      validateLocation(payloadUnflatten.location);

      const place = (await PostPlaceService.saveAndGetNearbyPlaceByLocation(
        {
          location: payloadUnflatten.location
        },
        { returnValue: 'placeObject' }
      )) as PlaceEntity;

      // although place not found, we still keep location
      if (place) {
        payloadUnflatten.placeId = place.id;
        payloadUnflatten.address = place.address;
      }
    } else if (payloadUnflatten.placeId) {
      const place = await PlaceService.getPlaceDetailOrSaveDataIfMissingDataByPlaceId(payloadUnflatten.placeId);
      if (!place) {
        throw new ValidationError(
          {
            placeId: {
              message: 'Place not found'
            }
          },
          {
            message: 'Place not found'
          }
        );
      }

      payloadUnflatten.address = place.address;
      payloadUnflatten.location = place.location;
    }
  }

  req.payload = flatten(payloadUnflatten);

  return req;
};

export const postResource = (awsCredentials: any) => ({
  resource: PostEntity,
  options: {
    navigation: {
      name: 'Post Management'
    },
    sort: {
      direction: 'desc',
      sortBy: 'createdAt'
    },
    properties: {
      photos: {
        isArray: true,
        type: 'string'
      },
      Photos: {
        isArray: true
      },
      title: {
        type: 'string'
      },
      type: {
        type: 'string',
        isRequired: true,
        availableValues: Object.keys(POST_TYPE)
          .filter(type => type !== POST_TYPE.leaveNote)
          .map(type => ({
            label: `${type[0].toUpperCase()}${type.slice(1, type.length)}`,
            value: type
          }))
      },
      status: {
        type: 'string',
        availableValues: Object.values(POST_STATUS).map(status => ({
          label: status.charAt(0).toUpperCase() + status.slice(1),
          value: status
        }))
      },
      source: {
        type: 'string',
        availableValues: Object.values(POST_SOURCE).map(status => ({
          label: status.charAt(0).toUpperCase() + status.slice(1),
          value: status
        }))
      },
      message: {
        type: 'textarea',
        isRequired: true,
        row: 4,
        components: {
          edit: Components.EditMessagePost,
          list: Components.ShortenText
        }
      },
      location: {
        type: 'mixed'
      },
      'location.lat': {
        type: 'string'
      },
      'location.long': {
        type: 'string'
      },
      tags: {
        isArray: true,
        type: 'string'
      },
      groupId: {
        reference: 'GroupEntity',
        description: "Applied to user's post"
      },
      placeId: {
        type: 'string',
        reference: 'PlaceEntity',
        components: {
          edit: Components.SelectGooglePlace
        }
      },
      moreInformation: {
        type: 'mixed',
        components: {
          show: Components.ShowMoreInformation
        }
      },
      createdBy: {
        reference: 'UserEntity'
      }
    },
    filterProperties: [
      'title',
      'isPin',
      'type',
      'status',
      'source',
      'groupId',
      'merchantId',
      // 'neighborhoodId',
      'createdBy'
    ],
    editProperties: [
      'title',
      'message',
      'groupId',
      // 'neighborhoodId',
      'createdBy',
      'createdByMerchantId',
      'type',
      'status',
      'Photos',
      'tags',
      'isPin',
      'pinOrder',
      'location',
      'placeId',
      // 'address',
      'startDate',
      'endDate'
    ],
    showProperties: [
      'title',
      'message',
      'groupId',
      // 'neighborhoodId',
      'createdBy',
      'createdByMerchantId',
      'type',
      'status',
      'source',
      'Photos',
      // 'photos',
      'tags',
      'isPin',
      'pinOrder',
      'location',
      'placeId',
      'address',
      'startDate',
      'endDate',
      'moreInformation'
    ],
    listProperties: [
      'title',
      'type',
      'status',
      'source',
      'isPin',
      'groupId',
      'merchantId',
      // 'neighborhoodId',
      'message',
      'privacy',
      'createdBy',
      'createdByMerchantId',
      'createdAt'
    ],
    actions: {
      new: {
        isVisible: true,
        before: async req => {
          req = await validateNewAndEditPost(req);

          return req;
        },
        after: async (res, req) => {
          if (String(req?.method).toLowerCase() === 'post') {
            const payload = req.payload;
            if (payload?.placeId) {
              const postRecord = res.record.params;
              PostPlaceService.upsertPostPlace(
                { postId: postRecord.id },
                {
                  placeId: postRecord.placeId,
                  postId: postRecord.id,
                  createdBy: postRecord.createdBy,
                  createdByMerchantId: postRecord.createdByMerchantId
                }
              ).catch(err => {
                console.log('error during upsertPostPlace', err);
              });
            }
          }

          return res;
        }
      },
      edit: {
        isVisible: true,
        before: async req => {
          if (String(req.method).toLowerCase() === 'post') {
            req = await validateNewAndEditPost(req);
          }

          return req;
        },
        after: async (res, req) => {
          if (String(req?.method).toLowerCase() === 'post') {
            const payload = req.payload;
            const postRecord = res.record.params;
            if (payload?.placeId) {
              PostPlaceService.upsertPostPlace(
                { postId: postRecord.id },
                {
                  placeId: postRecord.placeId,
                  postId: postRecord.id,
                  createdBy: postRecord.createdBy,
                  createdByMerchantId: postRecord.createdByMerchantId
                }
              ).catch(err => {
                console.error('error during upsertPostPlace', err);
              });
            } else {
              const existPostPlace = await PostPlaceEntity.findOne({ where: { postId: postRecord.id } });
              if (existPostPlace) {
                PostPlaceEntity.softRemove(existPostPlace).catch(err => {
                  console.error('error during PostPlaceEntity.softRemove', err);
                });
              }
            }
          } else {
            if (res.record?.params?.id) {
              const postPlace = await PostPlaceEntity.findOne({
                where: { postId: res.record.params.id },
                relations: ['place']
              });
              res.record.params['placeId'] = postPlace?.placeId;
              res.record.populated['postPlace'] = {
                params: postPlace,
                populated: {},
                bulkActions: [],
                recordActions: [],
                title: postPlace?.place?.name,
                id: postPlace?.id
              };
              res.record.populated['placeId'] = {
                params: postPlace?.place,
                populated: {},
                bulkActions: [],
                recordActions: [
                  {
                    actionType: 'record',
                    containerWidth: 1,
                    custom: {},
                    guard: '',
                    hasHandler: true,
                    hideActionHeader: false,
                    icon: 'Screen',
                    label: 'Show',
                    layout: null,
                    name: 'show',
                    parent: null,
                    resourceId: 'PlaceEntity',
                    showFilter: false,
                    showInDrawer: false,
                    showResourceActions: true,
                    variant: 'default'
                  }
                ],
                title: postPlace?.place?.name,
                id: postPlace?.placeId
              };
            }
          }

          if (res.record.populated?.createdBy?.params) {
            res.record.populated.createdBy.params = {
              id: res.record.populated.createdBy.params['id'],
              email: res.record.populated.createdBy.params['email'],
              phoneNumber: res.record.populated.createdBy.params['phoneNumber'],
              username: res.record.populated.createdBy.params['username'],
              'profile.firstName': res.record.populated.createdBy.params['profile.firstName'],
              'profile.lastName': res.record.populated.createdBy.params['profile.lastName'],
              'profile.avatar': res.record.populated.createdBy.params['profile.avatar']
            };
            res.record.populated.createdBy = handleResponseUser(res.record.populated?.createdBy);
          }

          return res;
        }
      },
      list: {
        before: (req, context) => {
          const typeValue =
            req.query['filters.type'] ||
            Object.values(POST_TYPE)
              .filter(type => type !== POST_TYPE.leaveNote)
              .toString();

          req.query['filters.type'] = typeValue;
          if (!context.resource.isCustomFindFn) {
            context.resource.isCustomFindFn = true;
            const oldFind = context.resource.find.bind(context.resource);

            context.resource.find = (filter, options) => {
              if (filter.filters.type) {
                const typeValue = filter.filters.type.value.split(',');
                filter.filters.type.custom = In(typeValue);
              }

              return oldFind(filter, options);
            };
          }

          return req;
        },
        after: res => {
          if (res.records?.length) {
            res.records = res.records.map(r => {
              if (typeof r.populated?.groupId === 'string') {
                r.populated.groupId = {
                  id: r.populated?.groupId,
                  recordActions: []
                };
              }
              if (typeof r.populated?.createdBy === 'string') {
                r.populated.createdBy = {
                  id: r.populated?.createdBy,
                  recordActions: []
                };
              } else {
                r.populated.createdBy = handleResponseUser(r.populated.createdBy);
              }
              if (typeof r.populated?.merchantId === 'string') {
                r.populated.merchantId = {
                  id: r.populated?.merchantId,
                  recordActions: []
                };
              }
              if (typeof r.populated?.neighborhoodId === 'string') {
                r.populated.neighborhoodId = {
                  id: r.populated?.neighborhoodId,
                  recordActions: []
                };
              }
              if (typeof r.populated?.createdByMerchantId === 'string') {
                r.populated.createdByMerchantId = {
                  id: r.populated?.createdByMerchantId,
                  recordActions: []
                };
              }

              return r;
            });
          }

          return res;
        }
      },
      show: {
        after: async res => {
          if (res.record.populated?.createdBy?.params) {
            res.record.populated.createdBy.params = {
              id: res.record.populated.createdBy.params['id'],
              email: res.record.populated.createdBy.params['email'],
              phoneNumber: res.record.populated.createdBy.params['phoneNumber'],
              username: res.record.populated.createdBy.params['username'],
              'profile.firstName': res.record.populated.createdBy.params['profile.firstName'],
              'profile.lastName': res.record.populated.createdBy.params['profile.lastName'],
              'profile.avatar': res.record.populated.createdBy.params['profile.avatar']
            };
            res.record.populated.createdBy = handleResponseUser(res.record.populated?.createdBy);
          }

          if (res.record?.params?.id) {
            const postPlace = await PostPlaceEntity.findOne({
              where: { postId: res.record.params.id },
              relations: ['place']
            });
            res.record.params['placeId'] = postPlace?.placeId;
            res.record.populated['postPlace'] = {
              params: postPlace,
              populated: {},
              bulkActions: [],
              recordActions: [],
              title: postPlace?.place?.name,
              id: postPlace?.id
            };
            res.record.populated['placeId'] = {
              params: postPlace?.place,
              populated: {},
              bulkActions: [],
              recordActions: [
                {
                  actionType: 'record',
                  containerWidth: 1,
                  custom: {},
                  guard: '',
                  hasHandler: true,
                  hideActionHeader: false,
                  icon: 'Screen',
                  label: 'Show',
                  layout: null,
                  name: 'show',
                  parent: null,
                  resourceId: 'PlaceEntity',
                  showFilter: false,
                  showInDrawer: false,
                  showResourceActions: true,
                  variant: 'default'
                }
              ],
              title: postPlace?.place?.name,
              id: postPlace?.placeId
            };
          }

          return res;
        }
      },
      import: {
        isAccessible: true,
        showInDrawer: false,
        showFilter: false,
        hideActionHeader: true,
        actionType: 'resource',
        component: AdminJS.bundle('../template/pages/ImportPostPage.jsx'),
        before: request => {
          const file = request.files?.file;
          if (!file) {
            throw new ValidationError(
              {
                file: {
                  message: 'File is required'
                }
              },
              {
                message: 'File is required'
              }
            );
          }
          if (!FileValidator.isExcelFile(file.type)) {
            throw new ValidationError(
              {
                file: {
                  message: 'Only accepts xlsx, csv files'
                }
              },
              {
                message: 'Only accepts xlsx, csv files'
              }
            );
          }
          ``;

          return request;
        },
        handler: async request => {
          const file = request.files.file;
          const limitSizeInMB = 25;
          const fileSizeInMB = +(file.size / 1024 / 1024).toFixed(1);

          if (fileSizeInMB > limitSizeInMB) {
            throw new ValidationError(
              {
                file: {
                  message: `File too large. Limit size: ${limitSizeInMB} Mb`
                }
              },
              {
                message: `File too large. Limit size: ${limitSizeInMB} Mb`
              }
            );
          }

          const workbook = XLSX.readFile(file.path, { type: 'buffer' });
          const sheetName = workbook.SheetNames[0];
          const sheet = workbook.Sheets[sheetName];
          const rawData: PostImport[] = XLSX.utils.sheet_to_json(sheet, {
            raw: true,
            blankrows: true,
            skipHidden: true
          });
          const data: PostImport[] = [];
          rawData.forEach((record, row) => {
            if (Object.keys(record).length) {
              // row started at 1;
              data.push({
                ...record,
                row: row + 1
              });
            }
          });
          const photosColumnIndex = 4;
          const zip = await jszip.loadAsync(fs.readFileSync(file.path));
          const images = [];
          const drawings = [];
          zip.forEach((relativePath, file) => {
            if (relativePath.startsWith('xl/media/')) {
              images.push(file);
            } else if (relativePath.startsWith('xl/drawings/drawing')) {
              drawings.push({ path: relativePath, file });
            }
          });

          const parser = new xml2js.Parser();

          const getImagesInRowColumn = async (): Promise<{
            [k: string]: { column: number; row: number; imageBuffer: Buffer; rEmbedId: string };
          }> => {
            return new Promise(async resolve => {
              try {
                const imgInExcel = {};
                for (const drawing of drawings) {
                  const drawingContent = await drawing.file.async('text');
                  parser.parseString(drawingContent, async (err, result) => {
                    if (err) throw err;

                    const oneCellAnchors = result['xdr:wsDr']['xdr:oneCellAnchor'] || [];
                    for (const anchor of oneCellAnchors) {
                      const column = anchor['xdr:from']?.[0]?.['xdr:col']?.[0];
                      const row = anchor['xdr:from']?.[0]?.['xdr:row']?.[0];
                      const rEmbedId = anchor['xdr:pic']?.[0]?.['xdr:blipFill']?.[0]?.['a:blip']?.[0]?.$?.['r:embed'];
                      const imageName = anchor['xdr:pic']?.[0]?.['xdr:nvPicPr']?.[0]?.['xdr:cNvPr']?.[0]?.$?.name;
                      const imageData = zip.file(`xl/media/${imageName}`);
                      if (imageData) {
                        const buffer = await imageData.async('nodebuffer');
                        const key = `row:${row}-column:${column}`;
                        imgInExcel[key] = { column, row, imageBuffer: buffer, rEmbedId };
                      }
                    }
                    resolve(imgInExcel);
                  });
                }
              } catch (error) {
                console.log('error getImages', error);
                resolve({});
              }
            });
          };

          const imagesByRowColumn = await getImagesInRowColumn();

          if (!data?.length) {
            throw new ValidationError(
              {
                file: {
                  message: "There's no data to import. Please ensure you have selected a file with compatible data"
                }
              },
              {
                message: "There's no data to import. Please ensure you have selected a file with compatible data"
              }
            );
          }
          let listUsernames = [];
          let listNeighborhoodCodes = [];
          const postsData = data.map(item => {
            const username = String(item.username || '').trim();
            const neighborhoodCode = +String(item['neighborhood code'] || '').trim();
            const title = String(item.title || '').trim();
            const message = String(item.message || '').trim();
            const photos = String(item.photos || '')
              .trim()
              .trim()
              .split(';')
              .map(p => p.trim())
              .filter(p => !!p);
            const type = String(item.type || '').trim();
            const tags = String(item.tags || '')
              .trim()
              .split(',')
              .map(t => t.trim())
              .filter(t => !!t);
            const locationStr = String(item.location || '').trim();
            if (!username) {
              throw new ValidationError(
                {
                  file: {
                    message: 'Username is required'
                  }
                },
                {
                  message: 'Username is required'
                }
              );
            }
            if (!neighborhoodCode) {
              throw new ValidationError(
                {
                  file: {
                    message: 'Neighborhood code is required'
                  }
                },
                {
                  message: 'Neighborhood code is required'
                }
              );
            }
            if (!title && !message) {
              throw new ValidationError(
                {
                  file: {
                    message: 'Title or Message is required'
                  }
                },
                {
                  message: 'Title or Message is required'
                }
              );
            }
            if (!type || ![POST_TYPE.ask, POST_TYPE.invite, POST_TYPE.share].includes(type as POST_TYPE)) {
              throw new ValidationError(
                {
                  file: {
                    message: `Type is required and must be a value in [${POST_TYPE.share}, ${POST_TYPE.ask}, ${POST_TYPE.invite}]`
                  }
                },
                {
                  message: `Type is required and must be a value in [${POST_TYPE.share}, ${POST_TYPE.ask}, ${POST_TYPE.invite}]`
                }
              );
            }
            let location;
            if (locationStr) {
              const [lat, long] = locationStr.split(',');
              if (!isLatitude(lat) || !isLongitude(long)) {
                throw new ValidationError(
                  {
                    file: {
                      message: `Location [${locationStr}] is invalid`
                    }
                  },
                  {
                    message: `Location [${locationStr}] is invalid`
                  }
                );
              }
              location = {
                lat,
                long
              };
            }
            listUsernames.push(item.username);
            listNeighborhoodCodes.push(neighborhoodCode);

            return {
              row: item.row,
              username,
              neighborhoodCode,
              title,
              message,
              type,
              photos,
              tags,
              location
            };
          });

          listUsernames = [...new Set(listUsernames)];
          listNeighborhoodCodes = [...new Set(listNeighborhoodCodes)];

          const [existUsernames, existNeighborhoods] = await Promise.all([
            UserEntity.find({ where: { username: In(listUsernames) } }),
            NeighborhoodEntity.find({ where: { district: In(listNeighborhoodCodes) } })
          ]);
          if (listUsernames.length !== existUsernames.length) {
            const usernameNotExist = listUsernames.find(username => {
              const foundUser = existUsernames.find(userInDb => userInDb.username === username);
              if (!foundUser) {
                return username;
              }
            });
            if (usernameNotExist) {
              throw new ValidationError(
                {
                  file: {
                    message: `Username [${usernameNotExist}] doesn't exist`
                  }
                },
                {
                  message: `Username [${usernameNotExist}] doesn't exist`
                }
              );
            }
          }
          if (listNeighborhoodCodes.length !== existNeighborhoods.length) {
            const codeNotExist = listNeighborhoodCodes.find(code => {
              const foundCode = existNeighborhoods.find(nbhInDb => nbhInDb.district === code);
              if (!foundCode) {
                return code;
              }
            });
            if (codeNotExist) {
              throw new ValidationError(
                {
                  file: {
                    message: `Neighborhood code [${codeNotExist}] doesn't exist`
                  }
                },
                {
                  message: `Neighborhood code [${codeNotExist}] doesn't exist`
                }
              );
            }
          }

          const mapUsernameWithId = existUsernames.reduce((obj, item) => {
            obj[item.username] = item.id;

            return obj;
          }, {});
          const mapNbhCodeWithId = existNeighborhoods.reduce((obj, item) => {
            obj[item.district] = item.id;

            return obj;
          }, {});

          const newPostEntities = await Promise.all(
            postsData.map(async post => {
              const postEntity = new PostEntity();

              const { username, neighborhoodCode, photos, ...postCanSave } = post;
              postEntity.createdBy = mapUsernameWithId[username];
              postEntity.neighborhoodId = mapNbhCodeWithId[neighborhoodCode];
              if (post.location) {
                const neighborhoodPlace = await PostNeighborhoodPlaceService.getNeighborhoodPlaceByLocation(
                  post.location
                );
                if (neighborhoodPlace) {
                  postEntity.neighborhoodPlaceId = neighborhoodPlace.id;
                }
              }
              let arrayPhotoUrlsS3 = await Promise.all(
                photos.map(async photoUrl => {
                  const imageBuffer = await getImageBuffer(photoUrl);
                  if (imageBuffer) {
                    const keyName = `post-image-${Date.now()}`;
                    try {
                      const result = await S3Driver.putObject(keyName, {
                        Body: imageBuffer
                      });

                      S3Driver.compressImage(result.keyName, {}).catch(error => {
                        console.log('Error when compressing image key name:', result.keyName, error);
                      });

                      return result.keyName;
                    } catch (error) {
                      console.log('Error when uploading image to s3: ', error);
                    }
                  }

                  return '';
                })
              );

              arrayPhotoUrlsS3 = arrayPhotoUrlsS3.filter(exist => exist);

              const rowColumnPhotoKey = `row:${post.row}-column:${photosColumnIndex}`;
              const imageBufferInRow = imagesByRowColumn[rowColumnPhotoKey];
              if (imageBufferInRow?.imageBuffer) {
                const keyName = `post-image-${Date.now()}`;
                const result = await S3Driver.putObject(keyName, {
                  Body: imageBufferInRow?.imageBuffer
                });
                arrayPhotoUrlsS3.push(result.keyName);
                S3Driver.compressImage(result.keyName, {}).catch(error => {
                  console.log('Error when compressing image key name:', result.keyName, error);
                });
              }

              postEntity.photos = arrayPhotoUrlsS3;
              Object.assign(postEntity, postCanSave);

              return postEntity;
            })
          );

          await PostEntity.save(newPostEntities);

          return {
            record: {},
            message: 'Imported successfully'
          };
        }
      },
      'search-v1': {
        actionType: 'resource',
        isVisible: false,

        handler: async req => {
          const query = req.query;
          const page = +query.page || 1;
          const perPage = +query.perPage || 10;
          const key = query.key ? String(query.key).trim() : '';

          const queryBuilder = PostEntity.createQueryBuilder('pst');

          if (key) {
            queryBuilder.where(
              new Brackets(qb => {
                qb.where('pst.title ILIKE :value', { value: `%${key}%` }).orWhere('pst.message ILIKE :value');
              })
            );
          }

          queryBuilder.andWhere({ status: POST_STATUS.activate });

          // default for ongoing and upcoming posts
          const now = new Date();
          queryBuilder.andWhere(
            `(
            (pst.start_date >= :now OR pst.end_date >= :now) OR
            (pst.start_date IS NULL AND pst.end_date IS NULL) OR
            (pst.start_date IS NULL AND pst.end_date IS NOT NULL AND pst.end_date >= :now) OR
            (pst.start_date IS NOT NULL AND pst.end_date IS NULL AND pst.start_date <= :now)
          )`,
            { now }
          );

          queryBuilder.orderBy('pst.startDate', 'ASC');

          // Pagination
          const paginationOptions = BaseDatabaseService.getPaginationOptions({
            page,
            limit: perPage
          } as BaseSearchDto);

          const { list, total } = await BaseDatabaseService.paginateAndCount(queryBuilder, paginationOptions);

          return {
            list: list,
            meta: {
              page,
              perPage,
              total
            }
          };
        }
      }
    }
  },
  features: [uploadImageFeature(awsCredentials, 'photos', { deleted: true, multiple: true, mimeType: '' })]
  // features: [
  //   uploadFeature({
  //     provider: {
  //       aws: awsCredentials
  //     },
  //     multiple: true,
  //     properties: {
  //       file: 'Photos',
  //       key: 'photos',
  //       filePath: 'filePathPhotos',
  //       filesToDelete: 'fileToDeletePhotos',
  //       mimeType: ''
  //     },
  //     validation: { mimeTypes: ['image/png', 'image/jpg', 'image/jpeg'] },
  //     uploadPath: (record, filename) => `asset/${record.id()}-${filename.replace(/ /g, '_')}`
  //   })
  // ]
});
