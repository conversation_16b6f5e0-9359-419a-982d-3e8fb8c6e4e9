import { ResourceWithOptions } from 'adminjs';
import { BotConfigEntity } from '@module/bot-config/entity/bot-config.entity';

export const botConfigResource: ResourceWithOptions = {
  resource: BotConfigEntity,
  options: {
    navigation: {
      name: 'Setting Management'
    },
    editProperties: ['enabled', 'cronSchedule', 'eventbrite', 'geminiAiGeneration', 'defaultEventOwnerId'],
    properties: {
      id: {
        isDisabled: true
      },
      enabled: {
        type: 'boolean'
      },
      cronSchedule: {
        type: 'string',
        props: {
          placeholder: 'Eg: 0 0 8-23 * * *',
          defaultValue: '0 0 8-23 * * *'
        }
      },
      eventbrite: {
        type: 'mixed'
      },
      'eventbrite.url': {
        type: 'string',
        props: {
          placeholder: 'https://www.eventbrite.com/d/singapore/all-events/',
          defaultValue: 'https://www.eventbrite.com/d/singapore/all-events/'
        }
      },
      'eventbrite.maxPages': {
        type: 'number'
      },
      'eventbrite.waitTimeMs': {
        type: 'number'
      },
      'eventbrite.enabled': {
        type: 'boolean'
      },
      geminiAiGeneration: {
        type: 'mixed'
      },
      'geminiAiGeneration.enabled': {
        type: 'boolean'
      },
      defaultEventOwnerId: {
        reference: 'UserEntity',
        isVisible: { list: true, filter: true, show: true, edit: true },
        props: {
          placeholder: 'Select default event Owner'
        }
      }
    }
  }
};
