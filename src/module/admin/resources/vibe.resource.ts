import { ValidationError, flatten, unflatten } from 'adminjs';
import { VibeEntity } from '@module/vibes/entity/vibe.entity';
import { VIBE_TYPE, DEFAULT_PROXIMITY_RADIUS_KM } from '@module/vibes/const';
import { handleResponseUser } from './utils/handle-response-user-info';

const validateNewAndEditActionVoucher = async request => {
  const payloadUnflatten = unflatten(request.payload);

  if (!payloadUnflatten.name) {
    throw new ValidationError(
      {
        name: {
          message: 'Name is required'
        }
      },
      {
        message: 'Name is required'
      }
    );
  }

  if (!payloadUnflatten.status) {
    throw new ValidationError(
      {
        status: {
          message: 'Status is required'
        }
      },
      {
        message: 'Status is required'
      }
    );
  }

  request.payload = flatten(payloadUnflatten);

  return request;
};

export const vibeResource = {
  resource: VibeEntity,
  options: {
    navigation: {
      name: 'Vibe Management'
    },
    sort: {
      direction: 'desc',
      sortBy: 'createdAt'
    },
    filterProperties: ['name', 'tagId', 'createdBy', 'createdAt'],
    listProperties: [
      'name',
      'description',
      'tagId',
      'status',
      'isPin',
      'timerHours',
      'proximityLock',
      'createdBy',
      'createdAt',
      'updatedAt'
    ],
    showProperties: [
      'name',
      'description',
      'tagId',
      'status',
      'location',
      'isPin',
      'pinOrder',
      'timerHours',
      'expiredAt',
      'proximityLock',
      'proximityRadiusKm',
      'createdBy',
      'createdAt',
      'updatedAt'
    ],
    editProperties: [
      'name',
      'description',
      'tagId',
      'status',
      'location',
      'isPin',
      'pinOrder',
      'expiredAt',
      'proximityLock',
      'proximityRadiusKm'
    ],
    properties: {
      tagId: {
        reference: 'TagEntity',
        isRequired: false
      },
      name: {
        type: 'string',
        isRequired: true
      },
      description: {
        type: 'textarea'
      },
      status: {
        type: 'string',
        isRequired: true
      },
      location: {
        type: 'mixed'
      },
      'location.lat': {
        type: 'string'
      },
      'location.long': {
        type: 'string'
      },
      timerHours: {
        type: 'number',
        description: 'Timer duration in hours (2, 4, 8, etc.) - Read only, calculated from expiredAt',
        isDisabled: true
      },
      expiredAt: {
        type: 'datetime',
        description: 'Set when the vibe will expire. TimerHours will be calculated automatically.',
        isRequired: false
      },
      proximityLock: {
        type: 'boolean',
        description: 'Restrict joining based on location proximity'
      },
      proximityRadiusKm: {
        type: 'number',
        description: `Radius in kilometers for proximity lock. Default: ${DEFAULT_PROXIMITY_RADIUS_KM}km when enabled`
      },
      isPin: {
        type: 'boolean',
        description: 'Pin this vibe to appear at the top of the list'
      },
      pinOrder: {
        type: 'number',
        description: 'Order of pinned vibes (lower numbers appear first). Only used when isPin is true.'
      }
    },
    actions: {
      new: {
        before: async req => {
          const { session } = req;
          req = await validateNewAndEditActionVoucher(req);
          req.payload.type = VIBE_TYPE.public;
          req.payload.createdBy = req.payload?.createdBy ? req.payload.createdBy : session?.adminUser?.id;
          req.payload.updatedBy = req.payload.createdBy;

          // Handle pinned fields validation
          if (req.payload.isPin !== 'true' && req.payload.isPin !== true) {
            req.payload.pinOrder = null;
          }

          // Handle expired_at and calculate timer hours
          if (req.payload.expiredAt) {
            const now = new Date();
            const expiredAt = new Date(req.payload.expiredAt);
            const timeDiffMs = expiredAt.getTime() - now.getTime();
            const timerHours = Math.round(timeDiffMs / (60 * 60 * 1000));
            req.payload.timerHours = timerHours > 0 ? timerHours : null;
          } else {
            req.payload.timerHours = null;
          }

          // Handle proximity lock and default radius
          if (req.payload.proximityLock === 'true' || req.payload.proximityLock === true) {
            if (!req.payload.proximityRadiusKm || req.payload.proximityRadiusKm === '') {
              req.payload.proximityRadiusKm = DEFAULT_PROXIMITY_RADIUS_KM;
            }
          } else {
            req.payload.proximityRadiusKm = null;
          }

          return req;
        }
      },
      edit: {
        isVisible: true,
        before: async req => {
          if (String(req.method).toLowerCase() === 'post') {
            const { session } = req;
            req = await validateNewAndEditActionVoucher(req);
            if (session?.adminUser?.id) {
              req.payload.updatedBy = session.adminUser.id;
            }

            // Handle pinned fields validation
            if (req.payload.isPin !== 'true' && req.payload.isPin !== true) {
              req.payload.pinOrder = null;
            }

            // Handle expired_at and calculate timer hours
            if (req.payload.expiredAt) {
              const now = new Date();
              const expiredAt = new Date(req.payload.expiredAt);
              const timeDiffMs = expiredAt.getTime() - now.getTime();
              const timerHours = Math.round(timeDiffMs / (60 * 60 * 1000));
              req.payload.timerHours = timerHours > 0 ? timerHours : null;
            } else if (req.payload.expiredAt === null || req.payload.expiredAt === '') {
              // If expiredAt is being cleared, also clear timerHours
              req.payload.timerHours = null;
            }

            // Handle proximity lock and default radius
            if (req.payload.proximityLock === 'true' || req.payload.proximityLock === true) {
              if (!req.payload.proximityRadiusKm || req.payload.proximityRadiusKm === '') {
                req.payload.proximityRadiusKm = DEFAULT_PROXIMITY_RADIUS_KM;
              }
            } else {
              req.payload.proximityRadiusKm = null;
            }
          }

          return req;
        }
      },
      list: {
        isVisible: true,
        before: req => {
          return req;
        },
        after: res => {
          if (res.records?.length) {
            res.records = res.records.map(r => {
              if (typeof r.populated?.tagId === 'string') {
                r.populated.tagId = {
                  id: r.populated?.tagId,
                  recordActions: []
                };
              }

              if (r.populated?.tagId?.params) {
                r.populated.tagId.title = r.populated?.tagId?.params?.tag || r?.populated?.params?.tag;
              }

              if (typeof r.populated?.createdBy === 'string') {
                r.populated.createdBy = {
                  id: r.populated?.createdBy,
                  recordActions: []
                };
              } else {
                r.populated.createdBy = handleResponseUser(r.populated.createdBy);
              }

              return r;
            });
          }

          return res;
        }
      },
      show: {
        isVisible: true,
        isAccessible: true,
        after: res => {
          if (res.record.populated?.tagId?.params) {
            res.record.populated.tagId.title =
              res.record.populated?.tagId?.params?.tag || res.record?.populated?.params?.tag;
          }
          if (typeof res.record.populated?.createdBy === 'string') {
            res.record.populated.createdBy = {
              id: res.record.populated?.createdBy,
              recordActions: []
            };
          } else {
            res.record.populated.createdBy = handleResponseUser(res.record.populated.createdBy);
          }

          return res;
        }
      }
    }
  }
};
