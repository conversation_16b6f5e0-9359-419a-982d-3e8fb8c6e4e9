import React, { useState, useEffect } from 'react';
import { Box, Button, FormGroup, Label, Input, Radio, MessageBox } from '@adminjs/design-system';
import { useNotice, ApiClient } from 'adminjs';
import { ReactSelect } from '../ReactSelect';
import { useForm, Controller } from 'react-hook-form';
import AlertMessage from '../base/AlertMessage';
import AsyncSelectSelect from 'react-select/async';
import debounce from 'lodash.debounce';

const CreateNotification = () => {
  const [users, setUsers] = useState([]);
  const [coupons, setCoupons] = useState([]);
  const [loading, setLoading] = useState(false);
  const [loadingCoupons, setLoadingCoupons] = useState(false);
  const [error, setError] = useState(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [inputValue, setInputValue] = useState('');
  const MODULES = [
    {
      value: 'coupon',
      label: 'Coupon'
    },
    {
      value: 'post',
      label: 'Post'
    }
  ];
  const sendNotice = useNotice();
  const api = new ApiClient();

  const {
    register,
    handleSubmit,
    control,
    watch,
    setValue,
    reset,
    formState: { errors, isSubmitting }
  } = useForm({
    defaultValues: {
      module: MODULES[0],
      title: '',
      body: '',
      description: '',
      sendToAllUsers: true,
      selectedUsers: [],
      selectedCoupon: null
    }
  });

  const sendToAllUsers = watch('sendToAllUsers');
  const module = watch('module')?.value;

  // Handle search input change with debounce
  useEffect(() => {
    const timer = setTimeout(() => {
      setSearchQuery(inputValue);
    }, 500); // 500ms debounce delay

    return () => clearTimeout(timer);
  }, [inputValue]);

  // Handle input change for search
  const handleSearchInputChange = e => {
    setInputValue(e.target.value);
  };

  // Fetch users for selection
  useEffect(() => {
    if (!sendToAllUsers) {
      setLoading(true);

      api
        .searchRecords({ resourceId: 'UserEntity', query: searchQuery })
        .then(array => {
          if (Array.isArray(array)) {
            setUsers(
              array.map(user => ({
                label: user.title || `${user.params.email || user.params.username || user.params.id}`,
                value: user.id
              }))
            );
          }
          setLoading(false);
        })
        .catch(err => {
          console.error('Error fetching users:', err);
          setError('Failed to load users');
          setLoading(false);
        });
    }
  }, [sendToAllUsers, searchQuery]);

  // Fetch coupons when coupon type is selected
  useEffect(() => {
    if (module === 'coupon') {
      setLoadingCoupons(true);

      // Use the search-v1 action to get valid coupons
      // This will use the server-side handler in coupon.resource.ts
      api
        .resourceAction({
          resourceId: 'CouponEntity',
          actionName: 'search-v1',
          method: 'get',
          params: { currentTime: new Date().toISOString() }
        })
        .then(response => {
          console.log('response', response);
          if (response?.data && Array.isArray(response?.data)) {
            setCoupons(
              response.data.map(coupon => ({
                label: `${coupon.title} (${coupon.code || 'No code'}) (EndTime:${
                  coupon.endTime ? new Date(coupon.endTime).toLocaleString() : 'No end time'
                })`,
                type: coupon.type,
                value: coupon.id
              }))
            );
          }
          setLoadingCoupons(false);
        })
        .catch(err => {
          console.error('Error fetching coupons:', err);
          sendNotice({ message: 'Failed to load coupons', type: 'error' });
          setLoadingCoupons(false);
        });
    }
  }, [module]);

  // AsyncPaginate loader for posts
  const loadPosts = async inputValue => {
    try {
      const res = await api.resourceAction({
        resourceId: 'PostEntity',
        actionName: 'search-v1',
        method: 'get',
        params: {
          key: inputValue,
          page: 1,
          perPage: 25
        }
      });

      const list = res?.data?.list || [];

      return list.map(pst => ({
        label: `${pst.title || pst.message || 'No title'} (${
          pst.startDate ? new Date(pst.startDate).toLocaleString() : 'No start date'
        } - ${pst.endDate ? new Date(pst.endDate).toLocaleString() : 'No end date'})`,
        value: pst.id,
        data: pst
      }));
    } catch (err) {
      console.error('Error fetching posts:', err);
      sendNotice({ message: 'Failed to load posts', type: 'error' });

      return [];
    }
  };

  // Debounced loader for posts
  const debouncedLoadPosts = React.useRef(
    debounce(async (inputValue, callback) => {
      const results = await loadPosts(inputValue);
      callback(results);
    }, 500)
  ).current;

  const loadPostsDebounced = React.useCallback(
    inputValue =>
      new Promise(resolve => {
        debouncedLoadPosts(inputValue, resolve);
      }),
    [debouncedLoadPosts]
  );

  const onSubmit = async data => {
    try {
      // Validate specific users selection if sendToAllUsers is false
      if (!data.sendToAllUsers && (!data.selectedUsers || data.selectedUsers.length === 0)) {
        sendNotice({ message: 'Please select at least one user', type: 'error' });

        return;
      }

      // Validate coupon selection if module is coupon
      if (data.module === 'coupon' && !data.selectedCoupon) {
        sendNotice({ message: 'Please select a coupon', type: 'error' });

        return;
      }

      if (data.module === 'post' && !data.selectedPost) {
        sendNotice({ message: 'Please select a post', type: 'error' });

        return;
      }

      const moreData = {
        description: data.description
      };
      if (data.module.value === 'coupon' && data.selectedCoupon) {
        moreData.referenceId = data.selectedCoupon.value;
        moreData.referenceEntity = 'CouponEntity';
        moreData.referenceEntityType = data.selectedCoupon.type;
      }

      if (data.module.value === 'post' && data.selectedPost) {
        moreData.referenceId = data.selectedPost.value;
        moreData.referenceEntity = 'PostEntity';
        moreData.referenceEntityType = data.selectedPost?.data?.type;
      }

      // Prepare notification data
      const notificationData = {
        notification: {
          title: data.title,
          body: data.body
        },
        sendToAllUsers: data.sendToAllUsers,
        userIds: data.selectedUsers.map(user => user.value),
        data: moreData
      };

      // Add your API call here to create notification
      const response = await fetch('/api/admin/api/resources/NotificationEntity/actions/send-notification', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(notificationData)
      });

      if (response.ok) {
        sendNotice({ message: 'Notification created successfully', type: 'success' });
        // Reset form after successful submission
        reset({
          module: MODULES[0],
          title: '',
          body: '',
          description: '',
          sendToAllUsers: true,
          selectedUsers: [],
          selectedCoupon: null
        });
      } else {
        sendNotice({ message: 'Error creating notification', type: 'error' });
      }
    } catch (error) {
      console.error('Error submitting notification:', error);
      sendNotice({ message: 'Error creating notification', type: 'error' });
    }
  };

  return (
    <Box variant="white">
      <Box variant="card">
        <form onSubmit={handleSubmit(onSubmit)}>
          <FormGroup>
            <Label required>Module</Label>
            <Controller
              name="module"
              control={control}
              rules={{ required: 'Module is required' }}
              render={({ field }) => <ReactSelect {...field} options={MODULES} />}
            />
            <AlertMessage type="error" title={errors.module?.message} />
          </FormGroup>

          <FormGroup>
            <Label required>Title</Label>
            <Input type="text" {...register('title', { required: 'Title is required' })} />
            <AlertMessage type="error" title={errors.title?.message} />
          </FormGroup>

          <FormGroup>
            <Label required>Body</Label>
            <Input as="textarea" rows={4} {...register('body', { required: 'Body is required' })} />
            <AlertMessage type="error" title={errors.body?.message} />
          </FormGroup>

          <FormGroup>
            <Label>Description (Optional)</Label>
            <Input type="text" {...register('description')} />
          </FormGroup>

          {module === 'coupon' && (
            <FormGroup>
              <Label required>Select Coupon</Label>
              {loadingCoupons ? (
                <Box>Loading coupons...</Box>
              ) : (
                <Controller
                  name="selectedCoupon"
                  control={control}
                  rules={{ required: 'Please select a coupon' }}
                  render={({ field }) => (
                    <ReactSelect
                      {...field}
                      options={coupons}
                      placeholder="Select a coupon"
                      styles={{
                        container: styles => ({
                          ...styles,
                          width: '100%'
                        })
                      }}
                    />
                  )}
                />
              )}
              <AlertMessage type="error" title={errors.selectedCoupon?.message} />
            </FormGroup>
          )}

          {module === 'post' && (
            <FormGroup>
              <Label required>Select Post</Label>
              <Controller
                name="selectedPost"
                control={control}
                rules={{ required: 'Please select a post' }}
                render={({ field }) => (
                  <AsyncSelectSelect
                    {...field}
                    value={field.value}
                    loadOptions={loadPostsDebounced}
                    defaultOptions
                    placeholder="Select a post"
                    isClearable
                    styles={{
                      container: styles => ({
                        ...styles,
                        width: '100%'
                      })
                    }}
                  />
                )}
              />
              <AlertMessage type="error" title={errors.selectedPost?.message} />
            </FormGroup>
          )}

          <FormGroup>
            <Label required>Recipients</Label>
            <Box style={{ marginBottom: '10px' }}>
              <Controller
                name="sendToAllUsers"
                control={control}
                render={({ field: { value, onChange } }) => (
                  <Box flexDirection="column" marginRight={15}>
                    <Radio
                      id="sendToAllUsers"
                      name="userSelection"
                      checked={value}
                      onChange={() => {
                        onChange(true);
                        setValue('selectedUsers', []);
                      }}
                      label="Send to all users"
                    />
                    <Label inline htmlFor="checkbox3" ml="default">
                      Send to all users
                    </Label>
                  </Box>
                )}
              />
            </Box>
            <Box>
              <Controller
                name="sendToAllUsers"
                control={control}
                render={({ field: { value, onChange } }) => (
                  <Box flexDirection="column" marginRight={15}>
                    <Radio
                      id="sendToSpecificUsers"
                      name="userSelection"
                      checked={!value}
                      onChange={() => onChange(false)}
                      label="Send to specific users"
                    />
                    <Label inline htmlFor="checkbox3" ml="default">
                      Send to specific users
                    </Label>
                  </Box>
                )}
              />
            </Box>
          </FormGroup>

          {!sendToAllUsers && (
            <FormGroup>
              <Label required>Select Users</Label>
              <Box marginBottom="10px">
                <Input
                  type="text"
                  placeholder="Search users..."
                  value={inputValue}
                  onChange={handleSearchInputChange}
                />
              </Box>
              {loading ? (
                <Box>Loading users...</Box>
              ) : error ? (
                <MessageBox message={error} variant="danger" />
              ) : (
                <Controller
                  name="selectedUsers"
                  control={control}
                  rules={{
                    validate: value =>
                      sendToAllUsers || (value && value.length > 0) || 'Please select at least one user'
                  }}
                  render={({ field }) => (
                    <ReactSelect
                      {...field}
                      isMulti
                      options={users}
                      placeholder="Select users"
                      styles={{
                        container: styles => ({
                          ...styles,
                          width: '100%'
                        })
                      }}
                    />
                  )}
                />
              )}
              <AlertMessage type="error" title={errors.selectedUsers?.message} />
            </FormGroup>
          )}

          <Button variant="primary" type="submit" disabled={isSubmitting}>
            Create Notification
          </Button>
        </form>
      </Box>
    </Box>
  );
};

export default CreateNotification;
