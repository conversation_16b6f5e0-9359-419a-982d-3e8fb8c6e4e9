import { Module } from '@nestjs/common';
import AdminJS from 'adminjs';
import { Database, Resource } from '@adminjs/typeorm';
import { AdminModule as AdminJsModule } from '@adminjs/nestjs';
import { validate } from 'class-validator';
import { userResource } from '@module/admin/resources/user.resource';
import { userProfileResource } from '@module/admin/resources/user-profile.resource';
import { listingResource } from '@module/admin/resources/listing.resource';
import { listingLikeResource } from '@module/admin/resources/listing-like.resource';
import { listingReviewResource } from '@module/admin/resources/listing-review.resource';
import { groupResource } from '@module/admin/resources/group.resource';
import { groupMemberResource } from '@module/admin/resources/group-member.resource';
import { orderResource } from '@module/admin/resources/order.resource';
import { orderHistoryResource } from '@module/admin/resources/order-history.resource';
import { announcementResource } from '@module/admin/resources/announcement.resource';
import { followResource } from '@module/admin/resources/follow.resource';
import { hashSync } from 'bcryptjs';
import { UserEntity } from '@core/user/entity/user.entity';
import { ConfigService } from '@nestjs/config';
import { merchantResource } from '@module/admin/resources/merchant.resource';
import { neighborhoodResource } from '@module/admin/resources/neighborhood.resource';
import { postResource } from '@module/admin/resources/post.resource';
import { postCommentResource } from '@module/admin/resources/comment.resource';
import { enLocale } from '@module/admin/locale';
import { postInviteResource } from '@module/admin/resources/post-invite.resource';
import { userExternalResource } from '@module/admin/resources/user-external.resource';
import { settingResource } from '@module/admin/resources/setting.resource';
import { USER_ROLE } from '@core/user/const/user-role';
import { userRoleResource } from '@module/admin/resources/user-role.resource';
import { cardResource } from '@module/admin/resources/card.resource';
import { couponResource } from '@module/admin/resources/coupon.resource';
import { neighborhoodPlaceResource } from '@module/admin/resources/neighborhood-place.resource';
import { pollResource } from '@module/admin/resources/poll.resource';
import { tagResource } from '@module/admin/resources/tag.resource';
import { placeResource } from '@module/admin/resources/place.resource';
import { placeCountResource } from '@module/admin/resources/place-count.resource';
import { placeTagResource } from '@module/admin/resources/place-tag.resource';
import { placeUserResource } from '@module/admin/resources/place-user.resource';
import { postPlaceResource } from '@module/admin/resources/post-place.resource';
import { collectionPlaceResource } from '@module/admin/resources/collection-place.resource';
import { placeCollectionResource } from '@module/admin/resources/place-collection.resource';
import { groupChatResource } from '@module/admin/resources/group-chat.resource';
import { groupChatMemberResource } from '@module/admin/resources/group-chat-member.resource';
import { groupCountResource } from '@module/admin/resources/group-count.resource';
import { placeUserCountResource } from '@module/admin/resources/place-user-count.resource';
import { mascotResource } from '@module/admin/resources/mascot.resource';
import { moderationResource } from '@module/admin/resources/moderation.resource';
import { postCountResource } from '@module/admin/resources/post-count.resource';
import { dashboardHandler } from './template/dashboard/dashboardHandler';
import { pointSettingResource } from '@module/admin/resources/point-setting.resource';
import { pointHistoryResource } from '@module/admin/resources/point-history.resource';
import { pointResource } from '@module/admin/resources/point.resource';
import { coinResource } from '@module/admin/resources/coin.resource';
import { coinHistoryResource } from '@module/admin/resources/coin-history.resource';
import { postTemplateResource } from '@module/admin/resources/post-template.resource';
import { postUnlockResource } from '@module/admin/resources/post-unlock.resource';
import { postLeaveNoteResource } from '@module/admin/resources/post-leave-note.resource';
// import { dealResource } from '@module/admin/resources/deal.resource';
import { merchantBranchResource } from '@module/admin/resources/merchant-branch.resource';
import { merchantTagResource } from '@module/admin/resources/merchant-tag.resource';
import { storeResource } from '@module/admin/resources/store.resource';
import { voucherResource } from '@module/admin/resources/voucher.resource';
import { vibeResource } from '@module/admin/resources/vibe.resource';
import { vibeUserResource } from '@module/admin/resources/vibe-user.resource';
import { couponMerchantBranchResource } from '@module/admin/resources/coupon-merchant-branch.resource';
import { listingOrderedResource } from '@module/admin/resources/listing-ordered.resource';
import { questResource } from '@module/admin/resources/quest.resource';
import { questUserResource } from '@module/admin/resources/quest-user.resource';
import { notificationResource } from '@module/admin/resources/notification.resource';
import { notificationDeviceResource } from '@module/admin/resources/notification-device.resource';
import { botConfigResource } from '@module/admin/resources/bot-config.resource';

Resource.validate = validate;
AdminJS.registerAdapter({ Database, Resource });

@Module({
  imports: [
    AdminJsModule.createAdminAsync({
      inject: [ConfigService],
      useFactory: async (configService: ConfigService) => {
        const awsCredentials = {
          region: configService.get('AWS_S3_REGION'),
          bucket: configService.get('AWS_S3_BUCKET_NAME'),
          accessKeyId: configService.get('AWS_S3_ACCESS_KEY_ID'),
          secretAccessKey: configService.get('AWS_S3_ACCESS_KEY_SECRET')
        };

        return {
          adminJsOptions: {
            rootPath: '/api/admin',
            loginPath: '/api/admin/login',
            logoutPath: '/api/admin/logout',
            branding: {
              companyName: 'Commun1ty',
              softwareBrothers: false
            },
            assets: {
              styles: ['/public/styles/common.css']
            },
            dashboard: {
              component: AdminJS.bundle('./template/dashboard/index'),
              handler: dashboardHandler
            },
            resources: [
              userResource,
              userRoleResource,
              userExternalResource,
              userProfileResource(awsCredentials),
              listingResource(awsCredentials),
              listingReviewResource(awsCredentials),
              listingLikeResource,
              groupResource(awsCredentials),
              merchantResource(awsCredentials),
              merchantBranchResource(awsCredentials),
              groupMemberResource,
              orderResource,
              orderHistoryResource,
              listingOrderedResource,
              announcementResource(awsCredentials),
              followResource,
              neighborhoodResource,
              postResource(awsCredentials),
              postCommentResource,
              postInviteResource,
              settingResource,
              botConfigResource,
              cardResource(awsCredentials),
              couponResource(awsCredentials),
              couponMerchantBranchResource,
              neighborhoodPlaceResource,
              pollResource,
              tagResource({
                ...awsCredentials,
                bucket:
                  configService.get('storage.s3.bucketStaticWebsiteName') || configService.get('AWS_S3_BUCKET_NAME')
              }),
              placeResource(awsCredentials),
              placeCountResource,
              placeTagResource,
              placeUserResource,
              placeUserCountResource,
              postPlaceResource,
              collectionPlaceResource(awsCredentials),
              placeCollectionResource,
              groupChatResource(awsCredentials),
              groupChatMemberResource,
              groupCountResource,
              mascotResource({
                ...awsCredentials,
                bucket: configService.get('storage.s3.bucketMascotsName') || configService.get('AWS_S3_BUCKET_NAME')
              }),
              moderationResource,
              postCountResource,
              pointSettingResource,
              pointResource,
              pointHistoryResource,
              coinResource,
              coinHistoryResource,
              postLeaveNoteResource(awsCredentials),
              postTemplateResource(awsCredentials),
              postUnlockResource,
              // dealResource(awsCredentials),
              merchantTagResource,
              storeResource(awsCredentials),
              voucherResource(awsCredentials),
              vibeResource,
              vibeUserResource,
              questResource,
              questUserResource,
              notificationResource,
              notificationDeviceResource
            ],
            locale: enLocale
          },
          // auth: {
          //   authenticate: async (email, password) => {
          //     const user = await UserEntity.findOne({ where: { email }, relations: ['userRole'] });
          //     if (!user) return null;
          //     if (user.password !== hashSync(password, user.salt)) return null;
          //     if (user?.userRole?.role !== USER_ROLE.systemAdmin) return null;

          //     return Promise.resolve({ email: user.email, id: user.id });
          //   },
          //   cookieName: 'auth',
          //   cookiePassword: hashSync((Math.floor(Math.random() * Math.pow(10, 10)) + Date.now()).toString(16))
          // },
          sessionOptions: {
            secret: hashSync((Math.floor(Math.random() * Math.pow(10, 10)) + Date.now()).toString(16)),
            resave: false,
            cookie: {
              maxAge: 1000 * 60 * 60 * 24 // 1 day
            }
          }
        };
      }
    })
  ]
})
export class AdminModule {}
