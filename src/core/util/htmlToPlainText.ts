export function htmlToPlainText(html: string) {
  return (
    (html || '')
      // Replace <br> and </p> with line breaks
      .replace(/<br\s*\/?>/gi, '\n')
      .replace(/<\/p>/gi, '\n\n')
      // Replace <li> with dash and space
      .replace(/<li>/gi, '')
      .replace(/<\/li>/gi, '\n')
      // Replace strong or other inline tags (remove tag only)
      .replace(/<\/?strong>/gi, '')
      // Remove all other tags
      .replace(/<[^>]*>/g, '')
      // Decode HTML entities
      .replace(/&amp;/g, '&')
      .replace(/&lt;/g, '<')
      .replace(/&gt;/g, '>')
      .replace(/&quot;/g, '"')
      .replace(/&#39;/g, "'")
      .trim()
  );
}
