import { USER_ROLE } from '@core/user/const/user-role';
import { UserRoleEntity } from '@core/user/entity/user-role.entity';
import { CanActivate, ExecutionContext, Injectable } from '@nestjs/common';

@Injectable()
export class IsSystemAdminGuard implements CanActivate {
  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();

    if (!request.user?.id) return false;

    const userRole = await UserRoleEntity.findOne({ where: { userId: request.user.id, role: USER_ROLE.systemAdmin } });

    if (!userRole) {
      return false;
    }

    return true;
  }
}
