import { <PERSON>Hand<PERSON> } from '@nestjs/cqrs/dist/decorators/events-handler.decorator';
import { IEventHandler } from '@nestjs/cqrs';
import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { VerifyPhoneNumberEvent } from '@core/auth/event/verify-phone-number.event';
import { USER_STATUS } from '@core/user/const/user-status';

@Injectable()
@EventsHandler(VerifyPhoneNumberEvent)
export class VerifyPhoneNumberEventHandler implements IEventHandler<VerifyPhoneNumberEvent> {
  constructor(private readonly configService: ConfigService) {}

  handle(event: VerifyPhoneNumberEvent) {
    const now = new Date();
    event.user.isVerifiedPhoneNumber = now;
    if (this.configService.get('auth.activateBy').includes('phoneNumber') && !event.user.activated) {
      event.user.activated = now;
      event.user.status = USER_STATUS.activated;
    }

    delete event.user.profile;
    event.user.save();
  }
}
