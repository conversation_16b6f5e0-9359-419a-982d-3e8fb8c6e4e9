import { <PERSON><PERSON>and<PERSON> } from '@nestjs/cqrs/dist/decorators/events-handler.decorator';
import { IEventHandler } from '@nestjs/cqrs';
import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { USER_STATUS } from '@core/user/const/user-status';
import { Num } from '@core/support/num';
import { VerifyEmailEvent } from '@core/auth/event/verify-email.event';

@Injectable()
@EventsHandler(VerifyEmailEvent)
export class VerifyEmailEventHandler implements IEventHandler<VerifyEmailEvent> {
  constructor(private readonly configService: ConfigService) {}

  handle(event: VerifyEmailEvent) {
    const now = new Date();
    event.user.isVerifiedEmail = now;
    if (this.configService.get('auth.activateBy').includes('email') && !event.user.activated) {
      event.user.activated = now;
      event.user.status = USER_STATUS.activated;
      event.user.code = Num.random(6);
    }

    delete event.user.profile;
    event.user.save();
  }
}
