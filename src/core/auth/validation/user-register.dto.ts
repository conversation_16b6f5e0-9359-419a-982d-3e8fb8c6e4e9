import { IsEmail, IsOptional, IsString, Length, Matches, MaxLength, <PERSON><PERSON>ength, Validate } from 'class-validator';
import { UserEntity } from '@core/user/entity/user.entity';
import { IsEntityNotExist } from '@core/support/validation/validator/is-entity-not-exist';
import { STRING_LENGTH } from '@core/database/schema/column';
import { BadRequestException } from '@nestjs/common';

export class UserRegisterDto {
  @MaxLength(STRING_LENGTH)
  @MinLength(1)
  @IsOptional()
  firstName: string;

  @MaxLength(STRING_LENGTH)
  @MinLength(1)
  @IsOptional()
  lastName: string;

  @Length(6)
  @IsOptional()
  postalCode: string;

  @Validate(IsEntityNotExist, [UserEntity, 'email', [], true], { message: 'This email is already existed!' })
  @IsEmail()
  @IsOptional()
  email: string;

  @Validate(IsEntityNotExist, [UserEntity, 'username', [], true], { message: 'This username is already existed!' })
  @Matches(/^[a-zA-Z0-9_.]+$/, { message: 'username only contains alphabet, numeric, underscore and point' })
  @Length(5, 20)
  @IsString()
  @IsOptional()
  username: string;

  @IsString()
  @MinLength(6)
  @Matches(/^\+[0-9]+$/, { message: 'phoneNumber is not valid' })
  @Validate(IsEntityNotExist, [UserEntity, 'phoneNumber', [], true], {
    message: 'This phoneNumber is already existed!'
  })
  @IsOptional()
  phoneNumber: string;

  @MaxLength(STRING_LENGTH)
  @MinLength(6)
  @Matches(/((?=.*\d)(?=.*\W+))(?![.\n])(?=.*[A-Z])(?=.*[a-z]).*$/, {
    message: 'password must contain at least one upper case, lower case, numeric, and special character'
  })
  password: string;

  extraValidation() {
    if (!(this.username || this.phoneNumber || this.email)) {
      throw new BadRequestException('At least one of fields is required: email, username, phoneNumber');
    }
  }
}
