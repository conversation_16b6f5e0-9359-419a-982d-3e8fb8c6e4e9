import { IsString, Length } from 'class-validator';
import { BadRequestException } from '@nestjs/common';
import { SendVerificationDto } from '@core/auth/validation/send-verification.dto';

export class VerifyDto extends SendVerificationDto {
  @IsString()
  @Length(6)
  code: string;

  extraValidation() {
    if (!this.username && !this.phoneNumber && !this.email) {
      throw new BadRequestException('One and only one of fields must be specified email, username, phoneNumber');
    }
  }
}
