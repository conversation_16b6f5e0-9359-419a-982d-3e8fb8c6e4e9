import { IsNotEmpty, <PERSON>String, <PERSON>, <PERSON><PERSON><PERSON>th, <PERSON><PERSON><PERSON><PERSON> } from 'class-validator';
import { BadRequestException } from '@nestjs/common';
import { SendVerificationDto } from '@core/auth/validation/send-verification.dto';
import { STRING_LENGTH } from '@core/database/schema/column';

export class ResetPasswordDto extends SendVerificationDto {
  @IsNotEmpty()
  @IsString()
  currentPassword: string;

  @MaxLength(STRING_LENGTH)
  @MinLength(6)
  @Matches(/((?=.*\d)(?=.*\W+))(?![.\n])(?=.*[A-Z])(?=.*[a-z]).*$/, {
    message: 'password must contain at least one upper case, lower case, numeric, and special character'
  })
  newPassword: string;

  extraValidation() {
    if (!this.username && !this.phoneNumber && !this.email) {
      throw new BadRequestException('One and only one of fields must be specified email, username, phoneNumber');
    }
  }
}
