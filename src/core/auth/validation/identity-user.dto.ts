import { IsEmail, IsOptional, IsString, Matches, MinLength, Validate } from 'class-validator';
import { IsEither } from '@core/support/validation/validator/is-either';
import { BadRequestException } from '@nestjs/common';

export class IdentityUserDto {
  @Validate(IsEither, ['email', 'username', 'phoneNumber'])
  @IsEmail()
  @IsOptional()
  email: string;

  @Validate(IsEither, ['email', 'username', 'phoneNumber'])
  @Matches(/^[a-z0-9_.]+$/, { message: 'username only contains alphabet, numeric, underscore and point' })
  @IsString()
  @IsOptional()
  username: string;

  @Validate(IsEither, ['email', 'username', 'phoneNumber'])
  @MinLength(6)
  @Matches(/^\+[0-9]+$/, { message: 'phoneNumber is not valid' })
  @IsString()
  @IsOptional()
  phoneNumber: string;

  extraValidation() {
    if (!this.username && !this.phoneNumber && !this.email) {
      throw new BadRequestException('One and only one of fields must be specified email, username, phoneNumber');
    }
  }
}
