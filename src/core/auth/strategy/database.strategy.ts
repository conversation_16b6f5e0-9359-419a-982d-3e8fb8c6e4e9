import { PassportStrategy } from '@nestjs/passport';
import { BadRequestException, Injectable, UnauthorizedException } from '@nestjs/common';
import { AuthService } from '../auth.service';
import { Strategy } from 'passport-custom';
import { USER_STATUS } from '@core/user/const/user-status';
import { ILike } from 'typeorm';

@Injectable()
export class DatabaseStrategy extends PassportStrategy(Strategy, 'database') {
  constructor(private readonly authService: AuthService) {
    super();
  }

  async validate(request: Request): Promise<any> {
    const body = request.body as unknown as { username: string; email: string; phoneNumber: string; password: string };
    let userCondition;
    if (!body || !body.password || !(body.username || body.email || body.phoneNumber)) {
      throw new BadRequestException('AUTH007');
    }
    if (body.username) {
      userCondition = {
        username: <PERSON><PERSON>(body.username)
      };
    } else if (body.email) {
      userCondition = {
        email: body.email
      };
    } else if (body.phoneNumber) {
      userCondition = {
        phoneNumber: body.phoneNumber
      };
    }
    const user = await this.authService.validateUser(userCondition, body.password);

    if (user.forceChangePassword) {
      throw new UnauthorizedException('AUTH001');
    }
    if (user.status === USER_STATUS.deactivated) {
      if (user.activated) {
        throw new UnauthorizedException('User not found');
      } else {
        throw new UnauthorizedException('AUTH002');
      }
    }

    return user;
  }
}
