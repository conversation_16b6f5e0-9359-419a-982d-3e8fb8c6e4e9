import { Injectable } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { ExtractJwt, Strategy } from 'passport-jwt';
import { ConfigService } from '@nestjs/config';
import { AuthJWTPayload } from '@core/auth/strategy/jwt.strategy';
import { RefreshTokenIssuer } from '@core/auth/token-issuer/refresh-token-issuer';

@Injectable()
export class RefreshTokenStrategy extends PassportStrategy(Strategy, 'RefreshTokenStrategy') {
  constructor(private readonly config: ConfigService, private readonly refreshTokenIssuer: RefreshTokenIssuer) {
    super({
      jwtFromRequest: ExtractJwt.fromBodyField('refreshToken'),
      ignoreExpiration: false,
      secretOrKey: config.get('auth.jwt.secret')
    });
  }

  async validate(payload: AuthJWTPayload) {
    return this.refreshTokenIssuer.validate(payload);
  }
}
