import { ExtractJwt, Strategy } from 'passport-jwt';
import { PassportStrategy } from '@nestjs/passport';
import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { AuthJWTPayload } from '@core/auth/strategy/jwt.strategy';
import { AuthTokenIssuer } from '@core/auth/token-issuer/auth-token-issuer';

@Injectable()
export class AccessTokenExpiredStrategy extends PassportStrategy(Strategy, 'AccessTokenExpiredStrategy') {
  constructor(private configService: ConfigService, private readonly authTokenIssuer: AuthTokenIssuer) {
    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      ignoreExpiration: true,
      secretOrKey: configService.get('auth.jwt.secret')
    });
  }

  validate(payload: AuthJWTPayload) {
    return this.authTokenIssuer.validate(payload);
  }
}
