import { ExtractJwt, Strategy } from 'passport-jwt';
import { PassportStrategy } from '@nestjs/passport';
import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { JWTPayload } from '@core/auth/token-issuer/base-token-issuer';
import { AuthTokenIssuer } from '@core/auth/token-issuer/auth-token-issuer';
import { UserEntity } from '@core/user/entity/user.entity';

export type AuthJWTPayload = JWTPayload & {
  userId: string;
  phoneNumber: string;
  merchantId?: string;
};

@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy) {
  constructor(private configService: ConfigService, private authTokenIssuer: AuthTokenIssuer) {
    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      ignoreExpiration: false,
      secretOrKey: configService.get('auth.jwt.secret')
    });
  }

  async validate(payload: AuthJWTPayload): Promise<UserEntity> {
    return this.authTokenIssuer.validate(payload);
  }
}
