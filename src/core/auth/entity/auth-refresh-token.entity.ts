import { Column, CreateDate<PERSON><PERSON>umn, <PERSON>tity, PrimaryGeneratedColumn, UpdateDateColumn } from 'typeorm';
import BaseEntity from '@core/database/entity/base-entity';
import { STRING_LENGTH } from '@core/database/schema/column';

@Entity('auth_refresh_token')
export class AuthRefreshTokenEntity extends BaseEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'user_id' })
  userId: string;

  @Column({ name: 'merchant_id' })
  merchantId: string;

  @Column({ name: 'ip' })
  ip: string;

  @Column({ name: 'user_agent' })
  userAgent: string;

  @Column({ name: 'token_hash', length: STRING_LENGTH })
  tokenHash: string;

  @CreateDateColumn({ name: 'created_at', type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at', type: 'timestamp', onUpdate: 'CURRENT_TIMESTAMP', nullable: true })
  updatedAt: Date;

  @Column({ name: 'expired_at' })
  expiredAt: Date;
}
