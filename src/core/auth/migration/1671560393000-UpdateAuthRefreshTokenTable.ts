import { MigrationInterface, QueryRunner } from 'typeorm';
import Column from '@core/database/schema/column';

export class UpdateAuthRefreshTokenTable1671560393000 implements MigrationInterface {
  tableName = 'auth_refresh_token';

  public async up(queryRunner: QueryRunner): Promise<any> {
    if (await queryRunner.hasTable(this.tableName)) {
      if (!(await queryRunner.hasColumn(this.tableName, 'merchant_id'))) {
        await queryRunner.addColumns(this.tableName, [Column.uuid('merchant_id').nullable()]);
      }
    }
  }

  public async down(queryRunner: QueryRunner): Promise<any> {
    if (await queryRunner.hasColumn(this.tableName, 'merchant_id')) {
      await queryRunner.dropColumn(this.tableName, 'merchant_id');
    }
  }
}
