import { MigrationInterface, QueryRunner } from 'typeorm';
import { Schema } from '@core/database/schema/schema';
import Column from '@core/database/schema/column';

export class CreateAuthRefreshTokenTable1647881749473 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<any> {
    await queryRunner.createTable(
      Schema.define('auth_refresh_token', [
        Column.uuid('id').setPrimary(),
        Column.uuid('user_id'),
        Column.string('token_hash'),
        Column.string('ip', 25).nullable(),
        Column.string('user_agent').nullable(),
        Column.timestamp('created_at').nullable(),
        Column.timestamp('expired_at').nullable(),
        Column.timestamp('updated_at').nullable()
      ]),
      true
    );
  }

  public async down(queryRunner: QueryRunner): Promise<any> {
    await queryRunner.dropTable('auth_refresh_token', true);
  }
}
