import { BaseTokenIssuer } from './base-token-issuer';
import { ForbiddenException, Injectable } from '@nestjs/common';
import { USER_STATUS } from '@core/user/const/user-status';
import { UserEntity } from '@core/user/entity/user.entity';
import { AuthJWTPayload } from '@core/auth/strategy/jwt.strategy';
import { MerchantEntity } from '@module/merchant/entity/merchant.entity';
// import { MERCHANT_STATUS } from '@module/merchant/const/merchant-status';

@Injectable()
export class AuthTokenIssuer extends BaseTokenIssuer {
  async validate(payload: AuthJWTPayload): Promise<any> {
    this.authorize(payload);
    // TODO: Optimize query
    const user = await UserEntity.findOneBy<UserEntity>({ id: payload.userId });
    if (user?.status === USER_STATUS.deactivated) {
      throw new ForbiddenException('AUTH002');
    }
    if (payload.merchantId) {
      const merchant = await MerchantEntity.findOneBy({ id: payload.merchantId });

      // if (!merchant || merchant.status === MERCHANT_STATUS.inactive) {
      if (!merchant) {
        throw new ForbiddenException('AUTH0010');
      }

      return { user, merchant };
    }

    return user;
  }
}
