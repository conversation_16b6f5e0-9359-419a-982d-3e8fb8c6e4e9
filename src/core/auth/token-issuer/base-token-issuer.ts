import { ForbiddenException, Injectable } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import * as jwt from 'jsonwebtoken';
import { v4 as uuid } from 'uuid';

export interface JWTPayload {
  issuerName?: string;
  uuid?: string;

  [key: string]: any;
}

export interface ITokenValidation {
  validate(payload: JWTPayload): any;
}

export { SignOptions } from 'jsonwebtoken';

@Injectable()
export class BaseTokenIssuer {
  static blackList: string[] = [];
  protected issuerName: string = this.constructor.name;

  constructor(readonly jwtService: JwtService) {}

  issue(payload: JWTPayload, options?: jwt.SignOptions) {
    return this.jwtService.sign(
      {
        issuerName: this.issuerName ?? this.constructor.name,
        uuid: uuid(),
        ...payload
      },
      options
    );
  }

  revoke(hash: string) {
    BaseTokenIssuer.blackList.push(hash);
  }

  verify(payload: JWTPayload) {
    return BaseTokenIssuer.blackList.indexOf(payload.uuid) === -1;
  }

  validate(payload: JWTPayload): any {
    this.authorize(payload);

    return false;
  }

  authorize(payload: JWTPayload) {
    if (payload.issuerName !== this.issuerName) {
      throw new ForbiddenException('Invalid token');
    }
  }
}
