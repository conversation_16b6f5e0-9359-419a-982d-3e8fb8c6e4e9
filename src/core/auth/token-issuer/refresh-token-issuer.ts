import { ForbiddenException, Injectable } from '@nestjs/common';
import { BaseTokenIssuer } from './base-token-issuer';
import { UserEntity } from '@core/user/entity/user.entity';
import { USER_STATUS } from '@core/user/const/user-status';
import { AuthJWTPayload } from '@core/auth/strategy/jwt.strategy';

@Injectable()
export class RefreshTokenIssuer extends BaseTokenIssuer {
  async validate(payload: AuthJWTPayload) {
    this.authorize(payload);

    const user = payload.userId && (await UserEntity.findOneBy({ id: payload.userId }));
    if (!user) {
      throw new ForbiddenException('AUTH003');
    }
    if (user.status === USER_STATUS.deactivated) {
      throw new ForbiddenException('AUTH002');
    }

    return user;
  }
}
