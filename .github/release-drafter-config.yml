name-template: 'v$RESOLVED_VERSION'
tag-template: 'v$RESOLVED_VERSION'
categories:
  - title: 'Features'
    labels:
      - 'feature'
  - title: 'Bug Fixes'
    labels:
      - 'bug'
autolabeler:
  - label: 'bug'
    branch:
      - '/fix-.+/'
      - '/fix\/.+/'
      - '/bug\/.+/'
      - '/bug-.+/'
      - '/bugfix\/.+/'
      - '/bugfix-.+/'
      - '/hotfix\/.+/'
      - '/hotfix-.+/'
  - label: 'feature'
    branch:
      - '/feature\/.+/'
      - '/feature-.+/'
      - '/update\/.+/'
      - '/update-.+/'
      - '/enhancement\/.+/'
      - '/enhancement-.+/'
change-template: '- $TITLE @$AUTHOR (#$NUMBER)'
change-title-escapes: '\<*_&' # You can add # and @ to disable mentions, and add ` to disable code blocks.
version-resolver:
  major:
    labels:
      - 'major'
  minor:
    labels:
      - 'minor'
  patch:
    labels:
      - 'patch'
  default: patch
template: |
  ## Changes

  $CHANGES