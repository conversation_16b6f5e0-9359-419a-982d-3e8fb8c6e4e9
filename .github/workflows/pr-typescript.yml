name: 'compiler'
on: [ pull_request ]

jobs:
  compile-typescript:
    name: 'typescript'
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - name: Setup Node
        uses: actions/setup-node@v3
        with:
          node-version: '16.13.0'
          cache: npm

      - name: Install Node modules
        run: npm install --ignore-scripts

      - name: Typescript Complier
        run: npx tsc
