name: Auto deploy development to EC2

concurrency:
  group: development
  cancel-in-progress: true

on:
  push:
    branches:
      - main

jobs:
  deploy:
    runs-on: ubuntu-latest
    environment: development
    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '16.13.0'

      - name: Install PM2
        run: npm install -g pm2

      - name: Copy Files via SSH
        uses: appleboy/scp-action@v0.1.2
        with:
          host: ${{ secrets.EC2_DEV_HOST }}
          username: ${{ secrets.USERNAME }}
          key: ${{ secrets.EC2_SSH_KEY }}
          source: "."
          target: "${{ secrets.TARGET_DIR_DEV }}"

      - name: Execute Remote Commands via SSH
        uses: appleboy/ssh-action@v0.1.4
        with:
          host: ${{ secrets.EC2_DEV_HOST }}
          username: ${{ secrets.USERNAME }}
          key: ${{ secrets.EC2_SSH_KEY }}
          script: |
            cd ${{ secrets.TARGET_DIR_DEV }}
            git pull
            npm install
            npm run build
            pm2 reload ecosystem.config.js --env development
