name: "Jest Coverage"
on: [ pull_request ]

jobs:
  coverage:
    runs-on: ubuntu-latest
    timeout-minutes: 15
    steps:
      - name: Checkout
        uses: actions/checkout@v3
        with:
          fetch-depth: 0

      - name: Setup Node
        uses: actions/setup-node@v3
        with:
          node-version: '16.13.0'
          cache: npm

      - name: Install Node modules
        run: npm install --ignore-scripts

      - name: Test Coverage
        uses: ArtiomTr/jest-coverage-report-action@v2
        with:
          threshold: 0
          test-script: npm run test:cov:changed
