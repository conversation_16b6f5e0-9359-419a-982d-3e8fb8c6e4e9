name: Manually deploy production to EC2

concurrency:
  group: production
  cancel-in-progress: true

# on:
#   push:
#     tags:
#       - 'v*'

on:
  # push:
  #   branches:
  #     - main
  # release:
  #   types: [published]
  workflow_dispatch:
    inputs:
      description:
        description: 'Write some description...'
        required: false
        default: ''
jobs:
  deployment:
    runs-on: ubuntu-latest
    environment: production
    steps:
      - name: Description
        run: echo "${{ github.event.inputs.description }}"

      - name: Checkout
        uses: actions/checkout@v3

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '16.13.0'

      - name: Install PM2
        run: npm install -g pm2

      - name: Copy Files via SSH
        uses: appleboy/scp-action@v0.1.2
        with:
          host: ${{ secrets.EC2_HOST_PROD }}
          username: ${{ secrets.USERNAME }}
          key: ${{ secrets.EC2_SSH_KEY }}
          source: "."
          target: "${{ secrets.TARGET_DIR }}"

      - name: Execute Remote Commands via SSH
        uses: appleboy/ssh-action@v0.1.4
        with:
          host: ${{ secrets.EC2_HOST_PROD }}
          username: ${{ secrets.USERNAME }}
          key: ${{ secrets.EC2_SSH_KEY }}
          script: |
            cd ${{ secrets.TARGET_DIR }}
            git pull
            npm install
            npm run build
            pm2 reload ecosystem.config.js --env production
