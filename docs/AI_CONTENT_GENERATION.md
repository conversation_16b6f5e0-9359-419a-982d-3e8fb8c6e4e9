# AI Content Generation Documentation

This document provides a comprehensive overview of the AI content generation functionality implemented across multiple modules in the Commun1ty platform.

## Overview

The Commun1ty platform leverages Google's Gemini AI to automate content generation for various features:

1. **Automated Post Creation**: Daily community posts on diverse topics
2. **Event Data Generation**: Structured information about upcoming events in Singapore

The AI content generation is built with a modular architecture that separates concerns and promotes reusability.

## Architecture

### Core Components

1. **AI Module** (`src/module/ai/`)
   - Provides the foundation for AI integration
   - Handles communication with Google's Gemini API
   - Offers reusable services for content generation

2. **Post Scheduler Module** (`src/module/post-scheduler/`)
   - Implements automated post creation using AI
   - Manages scheduling and execution of post generation jobs
   - Handles topic selection and post creation

3. **Event Scanner Module** (`src/module/event/`)
   - Generates structured event data using AI
   - Manages scheduling of event scanning jobs
   - Processes and stores event information

### Dependency Flow

```
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│                 │     │                 │     │                 │
│  AI Module      │◄────┤ Post Scheduler  │     │ Event Scanner   │
│  (GeminiService)│     │ Module          │     │ Module          │
│                 │     │                 │     │                 │
└────────┬────────┘     └─────────────────┘     └────────┬────────┘
         │                                               │
         │                                               │
         └───────────────────┬───────────────────────────┘
                             │
                             ▼
                     ┌─────────────────┐
                     │                 │
                     │  Google Gemini  │
                     │  AI API         │
                     │                 │
                     └─────────────────┘
```

## Content Generation Flow

### Post Generation Flow

1. **Trigger**: The post scheduler job runs at a scheduled time (or manually)
2. **Bot User**: The system identifies the designated bot user account
3. **Topic Selection**: A random topic is selected from a predefined list
4. **Neighborhood Selection**: A random neighborhood is selected from the database
5. **AI Prompt Construction**: A prompt is constructed requesting a post about the selected topic
6. **API Call**: The prompt is sent to Google's Gemini AI via the GeminiService
7. **Response Processing**: The AI response is parsed to extract title and content
8. **Uniqueness Check**: The system verifies the post title is unique for the bot user
9. **Post Creation**: A new post is created with the generated content and associated with the bot user

### Event Generation Flow

1. **Trigger**: The event scanner job runs at a scheduled time (or manually)
2. **Date Range Determination**: The system calculates the current date and 3-month future date
3. **AI Prompt Construction**: A detailed prompt is constructed requesting event data in JSON format
4. **API Call**: The prompt is sent to Google's Gemini AI via the GeminiService
5. **Response Processing**: The AI response is parsed from text to structured JSON
6. **Validation**: The event data is validated for required fields and format
7. **Storage**: Valid events are saved to JSON files and/or the database
8. **API Access**: The events are made available through API endpoints

## Key Technologies

- **NestJS**: Framework for building the backend services
- **Google Gemini AI**: Large language model for content generation
- **Axios**: HTTP client for API communication
- **Cron**: Scheduling library for automated jobs
- **TypeORM**: ORM for database interactions

## Configuration

### Environment Variables

The AI content generation requires the following environment variable:

```
GEMINI_API_KEY=your_gemini_api_key
```

### AI Model Configuration

The current implementation uses the `gemini-2.0-flash` model with the following configuration:

```typescript
generationConfig: {
  temperature: 0.1  // Lower value for more deterministic outputs
}
```

## Prompt Engineering

### Post Generation Prompt

```
Generate a social media post about ${topic}.
The post should be informative, engaging, and suitable for a community platform.

Format your response as follows:
Title: [A catchy, concise title for the post, max 100 characters]

[The main content of the post, 2-3 paragraphs, informative and engaging]
```

### Event Generation Prompt

```
Find a JSON array containing exactly 10 real upcoming events in Singapore, 
scheduled between ${currentDate} and ${next3Months}, strictly in Singapore Time (SGT).

Requirements:
+ Only include real events — no fabricated or fictional entries.
+ Event dates must be exact and fall within the specified range.
+ VenueName and VenueAddress must be valid and match the actual location of the event.
+ Include google map link in MoreInformation object.

Each event should include the following fields:
- title
- startDate
- endDate
- eventTime
- venue
- address
- eventType
- organizer
- ticketInfo
- description
- eventLink
- moreInformation
```

## Scheduling

### Post Scheduler

```typescript
// Run once a day at a random hour between 8 AM and 11 PM
time = '0 9 8-23 * * *';
```

### Event Scanner

```typescript
// Run once a day at intervals
time = '1 */2 8-23 * * *';
```

## Error Handling

The AI content generation includes robust error handling:

1. **API Communication Errors**: Failed requests are logged with detailed error information
2. **Parsing Errors**: Malformed responses are handled gracefully
3. **Missing API Keys**: The system provides clear warnings when API keys are missing
4. **Content Validation**: Generated content is validated before use

## Testing

### Manual Testing

1. **Post Generation**: 
   ```bash
   npx ts-node -r tsconfig-paths/register src/scripts/create-bot-post.ts
   ```

2. **Event Generation**:
   ```bash
   curl -X POST http://localhost:3000/api/event-scanner/scan
   ```

## Best Practices

1. **Prompt Design**: Carefully craft prompts to get consistent, structured outputs
2. **Error Handling**: Implement robust error handling for API failures
3. **Content Validation**: Always validate AI-generated content before using it
4. **Rate Limiting**: Be mindful of API rate limits and implement appropriate throttling
5. **Cost Management**: Monitor API usage to control costs

## Troubleshooting

### Common Issues

1. **Missing API Key**: Ensure the GEMINI_API_KEY is set in your environment
2. **Rate Limiting**: If you encounter rate limiting, implement exponential backoff
3. **Parsing Errors**: Check prompt formatting if response parsing fails
4. **Content Quality**: Adjust temperature and other parameters for better results

## Future Enhancements

Potential improvements for the AI content generation:

1. **Image Generation**: Add support for AI-generated images to accompany posts
2. **Content Moderation**: Implement AI-based content moderation for user-generated content
3. **Personalization**: Tailor generated content based on user preferences and behavior
4. **Multi-language Support**: Generate content in multiple languages
5. **Feedback Loop**: Implement a feedback mechanism to improve content quality over time
