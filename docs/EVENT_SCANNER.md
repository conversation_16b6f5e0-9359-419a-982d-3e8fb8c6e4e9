# Event Scanner Module

This module provides functionality to scan for events in Singapore using AI-generated content and output them in a structured JSON format for database storage.

## Features

- **AI-powered Event Generation**: Uses Google's Gemini AI to generate structured event data for Singapore events
- Collects events from today up to 3 months in the future
- Gathers detailed information for each event:
  - Event Title
  - Start Date and End Date
  - Event Time
  - Venue / Location (with full address)
  - Event Type / Category
  - Event Organizer / Host
  - Ticket Info (free or paid, with price if applicable)
  - Official Event Link or URL
  - Short Description
  - Additional information (like Google Maps links)
- Outputs the data in JSON format for database storage
- Scheduled to run automatically once a day at a random time between 8 AM and 11 PM
- Provides API endpoints to trigger scans manually and retrieve results
- Assigns random users as event creators
- Checks for duplicate events before saving to the database
- Retries with new events if all generated events are duplicates

## Setup

### 1. Environment Variables

Add the following environment variable to your `.env` file:

```
GEMINI_API_KEY=your_gemini_api_key
```

### 2. Add the Module to AppModule

The `PostModule` already includes the Event Scanner functionality, so no additional setup is required.

## Usage

### API Endpoints

The module exposes the following API endpoints:

- `GET /event-scanner/events` - Get the latest events
- `POST /event-scanner/scan` - Run a manual scan

### Running a Manual Scan

You can trigger a manual scan by making a POST request to the `/event-scanner/scan` endpoint:

```bash
curl -X POST http://localhost:3000/api/event-scanner/scan
```

### Scheduled Scans

The module is configured to run a scan automatically once a day at a random time between 8 AM and 11 PM. This randomization helps distribute the load and avoid predictable patterns.

## Output Format

The events are output in the following JSON format and saved to the database as PostEntity objects:

```json
{
  "title": "Event Title",
  "startDate": "2023-08-15T09:00:00.000Z",
  "endDate": "2023-08-15T17:00:00.000Z",
  "eventTime": "9:00 AM - 5:00 PM",
  "venue": "Venue Name",
  "address": "Full Address, Singapore",
  "eventType": "Technology",
  "organizer": "Organizer Name",
  "ticketInfo": {
    "isFree": false,
    "price": "50.00",
    "currency": "SGD"
  },
  "eventLink": "https://example.com/event",
  "googleMapLink": "https://maps.google.com/?q=Venue+Name,Singapore",
  "description": "Short description of the event.",
  "source": "aiGeneration",
  "createdBy": "userId",
  "type": "invite"
}
```

## Data Storage

The events are stored in database:
1. In the database as PostEntity objects with type "invite" and source "aiGeneration".

## AI Integration

The Event Scanner module integrates with Google's Gemini AI through the `GeminiEventService` to generate structured event data.

### How AI Event Generation Works

1. The system constructs a detailed prompt for Gemini AI that specifies:
   - Time period (current date to 3 months ahead)
   - Required event fields
   - Format constraints (JSON array)
   - Location constraints (Singapore only)
   - List of events to avoid (to prevent duplicates)
2. The AI generates a structured response with event data
3. The response is parsed and validated using a specialized JSON parser
4. Valid events are returned for storage
5. If all events are duplicates, the system retries with a new prompt that explicitly excludes the duplicate events

### AI Prompt

The event generation uses a carefully crafted prompt:

```
Find a JSON array containing exactly 2 real upcoming events in Singapore,
scheduled between [current date] and [3 months ahead], strictly in Singapore Time (SGT).

Requirements:
+ Only include real events — no fabricated or fictional entries.
+ Event dates must be exact and fall within the specified range.
+ VenueName and VenueAddress must be valid and match the actual location of the event.
+ Include google map link in MoreInformation object.
[List of events to avoid if any]

Each event should include the following fields:
- title
- startDate
- endDate
- eventTime
- venue
- address
- eventType
- organizer
- ticketInfo
- description
- eventLink (official event link)
- googleMapLink (link to google map)
- more (Object of more information)
```

## Duplicate Handling

The system checks for duplicate events before saving them to the database:

1. For each event, it checks if an event with the same title and start date already exists in the database
2. If a duplicate is found, the event is skipped
3. If all events are duplicates, the system retries with a new prompt that explicitly excludes the duplicate events

## Random User Assignment

Each event is assigned a random user as its creator:

1. The system queries for activated users in the database
2. It randomly selects users from this list
3. Each event is assigned one of these random users as its creator

## Scheduled Job

The event scanner job is scheduled to run at a different random time each day:

1. At midnight each day, the system schedules a job to run at a random time between 8 AM and 11 PM
2. This randomization helps distribute the load and avoid predictable patterns
3. The job runs the event scanner, which generates events and saves them to the database

## Extending the Module

### Customizing the AI Prompt

You can customize the AI prompt by modifying the `generateEventsByAI` method in `GeminiEventService`.

### Customizing the Scheduling

You can customize the scheduling by modifying the `setupCronJob` method in `EventScannerJobService`.
